from aiogram.fsm.state import StatesGroup, State
from common.homework.register_handlers import register_homework_handlers

# Используем конкретные состояния для преподавателя
class TeacherHomeworkStates(StatesGroup):
    main = State()  # Главное состояние для входа в раздел домашних заданий
    homework_menu = State()
    student_stats_course = State()
    student_stats_group = State()
    student_stats_lesson = State()
    student_stats_list = State()
    group_stats_group = State()
    group_stats_result = State()
    # Состояния для отправки сообщений
    enter_homework_message = State()
    confirm_homework_message = State()

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    TeacherHomeworkStates.main: None,  # None означает возврат в главное меню преподавателя
    TeacherHomeworkStates.homework_menu: TeacherHomeworkStates.main,
    TeacherHomeworkStates.student_stats_course: TeacherHomeworkStates.homework_menu,
    TeacherHomeworkStates.student_stats_group: TeacherHomeworkStates.student_stats_course,
    TeacherHomeworkStates.student_stats_lesson: TeacherHomeworkStates.student_stats_group,
    TeacherHomeworkStates.student_stats_list: TeacherHomeworkStates.student_stats_lesson,
    TeacherHomeworkStates.group_stats_group: TeacherHomeworkStates.homework_menu,
    TeacherHomeworkStates.group_stats_result: TeacherHomeworkStates.group_stats_group,
}

# Словарь обработчиков для каждого состояния (будет заполнен после регистрации)
STATE_HANDLERS = {}
