from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

def get_teacher_main_menu_kb() -> InlineKeyboardMarkup:
    """Клавиатура главного меню преподавателя"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [Inline<PERSON><PERSON>boardButton(text="👥 Моя группа", callback_data="teacher_my_group")],
        [InlineKeyboardButton(text="✅ Домашние задания", callback_data="teacher_homeworks")],
        [InlineKeyboardButton(text="📊 Аналитика", callback_data="teacher_analytics")],
        [InlineKeyboardButton(text="🧠 Тесты", callback_data="teacher_tests")]
    ])