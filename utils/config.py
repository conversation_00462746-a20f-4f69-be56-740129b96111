"""
Конфигурация приложения
"""
from os import getenv
from dotenv import load_dotenv

load_dotenv()

# Основные настройки
TOKEN = getenv("BOT_TOKEN")

# Webhook настройки
WEBHOOK_MODE = getenv("WEBHOOK_MODE", "false").lower() == "true"
WEBHOOK_HOST = getenv("WEBHOOK_HOST", "https://your-domain.com")
WEBHOOK_PATH = getenv("WEBHOOK_PATH", "/webhook")
WEBHOOK_URL = f"{WEBHOOK_HOST}{WEBHOOK_PATH}"

# Веб-сервер настройки
WEB_SERVER_HOST = getenv("WEB_SERVER_HOST", "0.0.0.0")
WEB_SERVER_PORT = int(getenv("WEB_SERVER_PORT", "8000"))

# Redis настройки
REDIS_ENABLED = getenv("REDIS_ENABLED", "false").lower() == "true"
REDIS_HOST = getenv("REDIS_HOST", "redis")
REDIS_PORT = int(getenv("REDIS_PORT", "6379"))
REDIS_DB = int(getenv("REDIS_DB", "0"))
REDIS_PASSWORD = getenv("REDIS_PASSWORD", None)

# Настройки уведомлений о домашних заданиях
HOMEWORK_NOTIFICATION_ENABLED = getenv("HOMEWORK_NOTIFICATION_ENABLED", "true").lower() == "true"
HOMEWORK_DAYS_THRESHOLD = float(getenv("HOMEWORK_DAYS_THRESHOLD", "5"))  # Дни для определения проблемного студента (может быть дробным)
HOMEWORK_CHECK_INTERVAL_MINUTES = float(getenv("HOMEWORK_CHECK_INTERVAL_MINUTES", "60"))  # Интервал проверки в минутах
HOMEWORK_DAILY_SUMMARY_HOUR = int(getenv("HOMEWORK_DAILY_SUMMARY_HOUR", "8"))  # Час отправки ежедневной сводки
HOMEWORK_DAILY_SUMMARY_MINUTE = int(getenv("HOMEWORK_DAILY_SUMMARY_MINUTE", "0"))  # Минута отправки ежедневной сводки

# Проверка обязательных переменных
if not TOKEN:
    raise ValueError("BOT_TOKEN не установлен в переменных окружения")

if WEBHOOK_MODE and WEBHOOK_HOST == "https://your-domain.com":
    raise ValueError("WEBHOOK_HOST должен быть настроен для webhook режима")
