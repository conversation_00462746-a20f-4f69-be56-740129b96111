"""
Репозиторий для работы с логами уведомлений
"""
import json
from datetime import datetime, timedelta
from typing import List, Optional
from sqlalchemy import select, and_, func
from sqlalchemy.orm import selectinload
from ..database import get_db_session
from ..models import NotificationLog, Curator, Student


class NotificationRepository:
    """Репозиторий для работы с логами уведомлений"""

    @staticmethod
    async def create_notification_log(curator_id: int, student_id: int, 
                                    notification_type: str, homework_ids: List[int]) -> NotificationLog:
        """Создать лог уведомления"""
        async with get_db_session() as session:
            notification_log = NotificationLog(
                curator_id=curator_id,
                student_id=student_id,
                notification_type=notification_type,
                homework_ids=json.dumps(homework_ids)
            )
            session.add(notification_log)
            await session.commit()
            await session.refresh(notification_log)
            return notification_log

    @staticmethod
    async def has_recent_notification(curator_id: int, student_id: int, 
                                    notification_type: str, hours: int = 24) -> bool:
        """Проверить, было ли недавнее уведомление"""
        async with get_db_session() as session:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            result = await session.execute(
                select(func.count(NotificationLog.id))
                .where(and_(
                    NotificationLog.curator_id == curator_id,
                    NotificationLog.student_id == student_id,
                    NotificationLog.notification_type == notification_type,
                    NotificationLog.sent_at >= cutoff_time
                ))
            )
            count = result.scalar() or 0
            return count > 0

    @staticmethod
    async def get_notifications_by_curator(curator_id: int, 
                                         days: int = 7) -> List[NotificationLog]:
        """Получить уведомления куратора за последние дни"""
        async with get_db_session() as session:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            result = await session.execute(
                select(NotificationLog)
                .options(
                    selectinload(NotificationLog.student).selectinload(Student.user),
                    selectinload(NotificationLog.curator).selectinload(Curator.user)
                )
                .where(and_(
                    NotificationLog.curator_id == curator_id,
                    NotificationLog.sent_at >= cutoff_time
                ))
                .order_by(NotificationLog.sent_at.desc())
            )
            return list(result.scalars().all())

    @staticmethod
    async def get_last_daily_summary(curator_id: int, student_id: int) -> Optional[NotificationLog]:
        """Получить последнюю ежедневную сводку для студента"""
        async with get_db_session() as session:
            result = await session.execute(
                select(NotificationLog)
                .where(and_(
                    NotificationLog.curator_id == curator_id,
                    NotificationLog.student_id == student_id,
                    NotificationLog.notification_type == 'daily_summary'
                ))
                .order_by(NotificationLog.sent_at.desc())
                .limit(1)
            )
            return result.scalar_one_or_none()

    @staticmethod
    async def cleanup_old_notifications(days: int = 30) -> int:
        """Удалить старые уведомления"""
        async with get_db_session() as session:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            result = await session.execute(
                select(func.count(NotificationLog.id))
                .where(NotificationLog.sent_at < cutoff_time)
            )
            count = result.scalar() or 0
            
            if count > 0:
                await session.execute(
                    NotificationLog.__table__.delete()
                    .where(NotificationLog.sent_at < cutoff_time)
                )
                await session.commit()
            
            return count

    @staticmethod
    async def get_notification_stats(curator_id: int, days: int = 30) -> dict:
        """Получить статистику уведомлений куратора"""
        async with get_db_session() as session:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            # Общее количество уведомлений
            total_result = await session.execute(
                select(func.count(NotificationLog.id))
                .where(and_(
                    NotificationLog.curator_id == curator_id,
                    NotificationLog.sent_at >= cutoff_time
                ))
            )
            total_notifications = total_result.scalar() or 0
            
            # Количество новых проблем
            new_problems_result = await session.execute(
                select(func.count(NotificationLog.id))
                .where(and_(
                    NotificationLog.curator_id == curator_id,
                    NotificationLog.notification_type == 'new_problem',
                    NotificationLog.sent_at >= cutoff_time
                ))
            )
            new_problems = new_problems_result.scalar() or 0
            
            # Количество ежедневных сводок
            daily_summaries_result = await session.execute(
                select(func.count(NotificationLog.id))
                .where(and_(
                    NotificationLog.curator_id == curator_id,
                    NotificationLog.notification_type == 'daily_summary',
                    NotificationLog.sent_at >= cutoff_time
                ))
            )
            daily_summaries = daily_summaries_result.scalar() or 0
            
            # Количество уникальных студентов
            unique_students_result = await session.execute(
                select(func.count(func.distinct(NotificationLog.student_id)))
                .where(and_(
                    NotificationLog.curator_id == curator_id,
                    NotificationLog.sent_at >= cutoff_time
                ))
            )
            unique_students = unique_students_result.scalar() or 0
            
            return {
                'total_notifications': total_notifications,
                'new_problems': new_problems,
                'daily_summaries': daily_summaries,
                'unique_students': unique_students,
                'period_days': days
            }
