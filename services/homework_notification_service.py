"""
Сервис для уведомлений о невыполненных домашних заданиях
"""
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Set, Tuple
from sqlalchemy import select, and_, func, not_
from sqlalchemy.orm import selectinload
from database import get_db_session
from database.database import _get_session_maker
from database.models import (
    Curator, Student, Group, Homework, HomeworkResult,
    Lesson, Subject, User, curator_groups, student_groups
)
from database.repositories import NotificationRepository
from utils.config import HOMEWORK_DAYS_THRESHOLD


class HomeworkNotificationService:
    """Сервис для работы с уведомлениями о домашних заданиях"""
    
    def __init__(self):
        self.notification_repo = NotificationRepository()
        self.logger = logging.getLogger(__name__)

    async def find_problem_students(self, days_threshold: float = None) -> Dict[int, List[Dict]]:
        """
        Найти проблемных студентов (не выполнивших ДЗ более N дней)

        Returns:
            Dict[curator_id, List[student_data]]
            где student_data = {
                'student': Student,
                'incomplete_homeworks': List[Homework]
            }
        """
        if days_threshold is None:
            days_threshold = HOMEWORK_DAYS_THRESHOLD

        session_maker = _get_session_maker()
        async with session_maker() as session:
            cutoff_date = datetime.now() - timedelta(days=days_threshold)
            
            # Получаем все домашние задания старше порогового времени
            old_homeworks_result = await session.execute(
                select(Homework)
                .options(
                    selectinload(Homework.lesson),
                    selectinload(Homework.subject)
                )
                .where(Homework.created_at <= cutoff_date)
            )
            old_homeworks = list(old_homeworks_result.scalars().all())
            
            if not old_homeworks:
                return {}
            
            # Получаем только кураторов (исключаем админов) с их группами и студентами
            curators_result = await session.execute(
                select(Curator)
                .join(User, Curator.user_id == User.id)
                .where(User.role == 'curator')  # Только кураторы, не админы
                .options(
                    selectinload(Curator.user),
                    selectinload(Curator.groups).selectinload(Group.students).selectinload(Student.user),
                    selectinload(Curator.groups).selectinload(Group.students).selectinload(Student.groups).selectinload(Group.subject),
                    selectinload(Curator.groups).selectinload(Group.subject)
                )
            )
            curators = list(curators_result.scalars().all())
            
            problem_students_by_curator = {}
            
            for curator in curators:
                curator_problems = []

                # Получаем предметы ЭТОГО куратора через его группы
                curator_subject_ids = set()
                for group in curator.groups:
                    if group.subject_id:
                        curator_subject_ids.add(group.subject_id)

                # Получаем студентов только из групп ЭТОГО куратора
                curator_students = set()
                for group in curator.groups:
                    curator_students.update(group.students)

                for student in curator_students:
                    # Фильтруем домашние задания только по предметам ЭТОГО куратора
                    relevant_homeworks = [
                        hw for hw in old_homeworks
                        if hw.subject_id in curator_subject_ids
                    ]
                    
                    if not relevant_homeworks:
                        continue
                    
                    # Проверяем, какие ДЗ не выполнены
                    incomplete_homeworks = []
                    
                    for homework in relevant_homeworks:
                        # Проверяем, есть ли результат выполнения этого ДЗ
                        result_check = await session.execute(
                            select(func.count(HomeworkResult.id))
                            .where(and_(
                                HomeworkResult.student_id == student.id,
                                HomeworkResult.homework_id == homework.id
                            ))
                        )
                        has_result = (result_check.scalar() or 0) > 0
                        
                        if not has_result:
                            incomplete_homeworks.append(homework)
                    
                    # Если есть невыполненные ДЗ, добавляем студента в проблемные
                    if incomplete_homeworks:
                        curator_problems.append({
                            'student': student,
                            'incomplete_homeworks': incomplete_homeworks
                        })
                
                if curator_problems:
                    problem_students_by_curator[curator.id] = curator_problems
            
            return problem_students_by_curator

    async def find_new_problem_students(self, days_threshold: float = None) -> Dict[int, List[Dict]]:
        """
        Найти НОВЫХ проблемных студентов (которым еще не отправляли уведомления)
        """
        all_problems = await self.find_problem_students(days_threshold)
        new_problems = {}
        
        for curator_id, student_problems in all_problems.items():
            new_student_problems = []
            
            for problem_data in student_problems:
                student = problem_data['student']
                
                # Проверяем, было ли недавнее уведомление о новой проблеме
                has_recent = await self.notification_repo.has_recent_notification(
                    curator_id=curator_id,
                    student_id=student.id,
                    notification_type='new_problem',
                    hours=24  # Не дублируем уведомления в течение суток
                )
                
                if not has_recent:
                    new_student_problems.append(problem_data)
            
            if new_student_problems:
                new_problems[curator_id] = new_student_problems
        
        return new_problems

    async def get_daily_summary_students(self, days_threshold: float = None) -> Dict[int, List[Dict]]:
        """
        Получить всех проблемных студентов для ежедневной сводки
        """
        return await self.find_problem_students(days_threshold)

    def format_homework_message(self, student_data: Dict) -> str:
        """
        Форматировать сообщение о невыполненных ДЗ студента
        """
        student = student_data['student']
        incomplete_homeworks = student_data['incomplete_homeworks']

        # Заголовок с информацией о студенте
        message = f"👤 <b>{student.user.name}</b>\n"
        # message += f"📱 Telegram ID: {student.user.telegram_id}\n\n"

        # Группируем ДЗ по предметам
        homeworks_by_subject = {}
        for homework in incomplete_homeworks:
            subject_name = homework.subject.name
            if subject_name not in homeworks_by_subject:
                homeworks_by_subject[subject_name] = []
            homeworks_by_subject[subject_name].append(homework)

        # Формируем сообщение по предметам
        message += "📚 <b>Невыполненные домашние задания:</b>\n\n"

        for subject_name, homeworks in homeworks_by_subject.items():
            message += f"📖 <b>{subject_name}:</b>\n"

            for i, homework in enumerate(homeworks, 1):
                days_overdue = (datetime.now() - homework.created_at).days
                message += f"   {i}. <b>{homework.lesson.name}</b> - {homework.name}\n"
                message += f"      📅 Создано: {homework.created_at.strftime('%d.%m.%Y')}"
                message += f" ({days_overdue} дн. назад)\n"

            message += "\n"

        return message

    async def send_notification_to_curator(self, bot, curator_id: int, student_data: Dict, 
                                         notification_type: str) -> bool:
        """
        Отправить уведомление куратору о проблемном студенте
        """
        try:
            # Получаем куратора
            session_maker = _get_session_maker()
            async with session_maker() as session:
                curator_result = await session.execute(
                    select(Curator)
                    .options(selectinload(Curator.user))
                    .where(Curator.id == curator_id)
                )
                curator = curator_result.scalar_one_or_none()
                
                if not curator:
                    self.logger.error(f"Куратор с ID {curator_id} не найден")
                    return False
                
                # Формируем сообщение
                message = self.format_homework_message(student_data)
                
                # Добавляем заголовок в зависимости от типа уведомления
                if notification_type == 'new_problem':
                    header = "🚨 <b>НОВЫЙ ПРОБЛЕМНЫЙ СТУДЕНТ</b>\n\n"
                else:  # daily_summary
                    header = "📊 <b>ЕЖЕДНЕВНАЯ СВОДКА</b>\n\n"
                
                full_message = header + message
                
                # Создаем инлайн-кнопку для отправки сообщения студенту
                from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

                student_id = student_data['student'].id
                print(f"DEBUG: Создаем кнопку для студента ID: {student_id}")

                keyboard = InlineKeyboardMarkup(inline_keyboard=[
                    [InlineKeyboardButton(
                        text="💬 Отправить сообщение студенту",
                        callback_data=f"send_message_to_student:{student_id}"
                    )]
                ])

                # Отправляем сообщение с кнопкой
                await bot.send_message(
                    chat_id=curator.user.telegram_id,
                    text=full_message,
                    parse_mode='HTML',
                    reply_markup=keyboard
                )
                
                # Логируем отправку
                homework_ids = [hw.id for hw in student_data['incomplete_homeworks']]
                await self.notification_repo.create_notification_log(
                    curator_id=curator_id,
                    student_id=student_data['student'].id,
                    notification_type=notification_type,
                    homework_ids=homework_ids
                )
                
                self.logger.info(
                    f"Отправлено уведомление куратору {curator.user.name} "
                    f"о студенте {student_data['student'].user.name}"
                )
                
                return True
                
        except Exception as e:
            self.logger.error(f"Ошибка отправки уведомления куратору {curator_id}: {e}")
            return False

    async def process_new_problems(self, bot) -> int:
        """
        Обработать новых проблемных студентов (отправить уведомления)
        
        Returns:
            Количество отправленных уведомлений
        """
        new_problems = await self.find_new_problem_students()
        notifications_sent = 0
        
        for curator_id, student_problems in new_problems.items():
            for student_data in student_problems:
                success = await self.send_notification_to_curator(
                    bot=bot,
                    curator_id=curator_id,
                    student_data=student_data,
                    notification_type='new_problem'
                )
                if success:
                    notifications_sent += 1
        
        self.logger.info(f"Обработка новых проблем завершена. Отправлено уведомлений: {notifications_sent}")
        return notifications_sent

    async def process_daily_summary(self, bot) -> int:
        """
        Обработать ежедневную сводку (отправить всем кураторам)
        
        Returns:
            Количество отправленных уведомлений
        """
        all_problems = await self.get_daily_summary_students()
        notifications_sent = 0
        
        for curator_id, student_problems in all_problems.items():
            for student_data in student_problems:
                success = await self.send_notification_to_curator(
                    bot=bot,
                    curator_id=curator_id,
                    student_data=student_data,
                    notification_type='daily_summary'
                )
                if success:
                    notifications_sent += 1
        
        self.logger.info(f"Ежедневная сводка завершена. Отправлено уведомлений: {notifications_sent}")
        return notifications_sent
