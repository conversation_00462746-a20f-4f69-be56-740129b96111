"""
Простой планировщик без APScheduler для тестирования
"""
import asyncio
import logging
from datetime import datetime, time
from typing import Optional

from .homework_notification_service import HomeworkNotificationService
from utils.config import (
    HOMEWORK_NOTIFICATION_ENABLED,
    HOMEWORK_CHECK_INTERVAL_MINUTES,
    HOMEWORK_DAILY_SUMMARY_HOUR,
    HOMEWORK_DAILY_SUMMARY_MINUTE
)


class SimpleNotificationScheduler:
    """Простой планировщик для автоматических уведомлений о домашних заданиях"""
    
    def __init__(self, bot):
        self.bot = bot
        self.notification_service = HomeworkNotificationService()
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.tasks = []
        
    async def start(self):
        """Запустить планировщик"""
        if not HOMEWORK_NOTIFICATION_ENABLED:
            self.logger.info("📴 Уведомления о домашних заданиях отключены в конфигурации")
            return
            
        self.running = True
        self.logger.info("✅ Простой планировщик уведомлений запущен")
        
        # Запускаем задачу проверки новых проблем
        check_task = asyncio.create_task(self._check_new_problems_loop())
        self.tasks.append(check_task)
        
        # Запускаем задачу ежедневной сводки
        summary_task = asyncio.create_task(self._daily_summary_loop())
        self.tasks.append(summary_task)
        
        self.logger.info(f"📅 Проверка новых проблем: каждые {HOMEWORK_CHECK_INTERVAL_MINUTES} минут")
        self.logger.info(f"📅 Ежедневная сводка: {HOMEWORK_DAILY_SUMMARY_HOUR}:{HOMEWORK_DAILY_SUMMARY_MINUTE:02d}")

    async def stop(self):
        """Остановить планировщик"""
        self.running = False
        
        # Отменяем все задачи
        for task in self.tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        self.tasks.clear()
        self.logger.info("🛑 Простой планировщик уведомлений остановлен")

    async def _check_new_problems_loop(self):
        """Цикл проверки новых проблемных студентов"""
        while self.running:
            try:
                await self._check_new_problems()
                
                # Ждем следующую проверку
                await asyncio.sleep(HOMEWORK_CHECK_INTERVAL_MINUTES * 60)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"❌ Ошибка в цикле проверки новых проблем: {e}")
                # Ждем минуту перед повторной попыткой
                await asyncio.sleep(60)

    async def _daily_summary_loop(self):
        """Цикл ежедневной сводки"""
        while self.running:
            try:
                now = datetime.now()
                target_time = now.replace(
                    hour=HOMEWORK_DAILY_SUMMARY_HOUR,
                    minute=HOMEWORK_DAILY_SUMMARY_MINUTE,
                    second=0,
                    microsecond=0
                )
                
                # Если время уже прошло сегодня, планируем на завтра
                if now >= target_time:
                    target_time = target_time.replace(day=target_time.day + 1)
                
                # Вычисляем время ожидания
                wait_seconds = (target_time - now).total_seconds()
                
                self.logger.info(f"📅 Следующая ежедневная сводка: {target_time}")
                
                # Ждем до нужного времени
                await asyncio.sleep(wait_seconds)
                
                # Отправляем сводку
                if self.running:
                    await self._daily_summary()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"❌ Ошибка в цикле ежедневной сводки: {e}")
                # Ждем час перед повторной попыткой
                await asyncio.sleep(3600)

    async def _check_new_problems(self):
        """Проверить новых проблемных студентов"""
        try:
            self.logger.info("🔍 Начинаем проверку новых проблемных студентов...")
            
            notifications_sent = await self.notification_service.process_new_problems(self.bot)
            
            if notifications_sent > 0:
                self.logger.info(f"✅ Проверка завершена. Отправлено {notifications_sent} уведомлений")
            else:
                self.logger.info("✅ Проверка завершена. Новых проблемных студентов не найдено")
                
        except Exception as e:
            self.logger.error(f"❌ Ошибка при проверке новых проблем: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    async def _daily_summary(self):
        """Отправить ежедневную сводку"""
        try:
            self.logger.info("📊 Начинаем отправку ежедневной сводки...")
            
            notifications_sent = await self.notification_service.process_daily_summary(self.bot)
            
            if notifications_sent > 0:
                self.logger.info(f"✅ Ежедневная сводка завершена. Отправлено {notifications_sent} уведомлений")
            else:
                self.logger.info("✅ Ежедневная сводка завершена. Проблемных студентов не найдено")
                
        except Exception as e:
            self.logger.error(f"❌ Ошибка при отправке ежедневной сводки: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    async def run_manual_check(self) -> dict:
        """Запустить ручную проверку новых проблем"""
        try:
            self.logger.info("🔧 Запуск ручной проверки новых проблемных студентов...")
            
            notifications_sent = await self.notification_service.process_new_problems(self.bot)
            
            return {
                'success': True,
                'notifications_sent': notifications_sent,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка ручной проверки: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def get_scheduler_status(self) -> dict:
        """Получить статус планировщика"""
        try:
            return {
                'running': self.running,
                'tasks_count': len(self.tasks),
                'check_interval_minutes': HOMEWORK_CHECK_INTERVAL_MINUTES,
                'daily_summary_time': f"{HOMEWORK_DAILY_SUMMARY_HOUR}:{HOMEWORK_DAILY_SUMMARY_MINUTE:02d}",
                'scheduler_type': 'simple_asyncio'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка получения статуса планировщика: {e}")
            return {'running': False, 'error': str(e)}
