from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.keyboards import get_main_menu_back_button
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта для импорта database
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database import CuratorRepository, SubjectRepository

def get_manager_analytics_menu_kb() -> InlineKeyboardMarkup:
    """Клавиатура меню аналитики менеджера"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📊 Статистика по ученику", callback_data="manager_student_analytics")],
        [InlineKeyboardButton(text="📈 Статистика по группе", callback_data="manager_group_analytics")],
        [InlineKeyboardButton(text="📚 Статистика по предмету", callback_data="manager_subject_analytics")],
        [InlineKeyboardButton(text="🎓 Статистика по курсу", callback_data="manager_course_analytics")],
        [InlineKeyboardButton(text="📋 Общая статистика", callback_data="general_analytics")],
        *get_main_menu_back_button()
    ])

async def get_staff_kb(staff_type: str = "curator") -> InlineKeyboardMarkup:
    """
    Универсальная клавиатура выбора персонала

    Args:
        staff_type: Тип сотрудников ("curator" или "teacher")
    """
    # Инициализируем переменные по умолчанию
    if staff_type == "curator":
        staff_name_plural = "Кураторы"
        callback_prefix = "manager_curator_"
    elif staff_type == "teacher":
        staff_name_plural = "Преподаватели"
        callback_prefix = "manager_teacher_"
    else:
        staff_name_plural = "Сотрудники"
        callback_prefix = "manager_staff_"

    try:
        if staff_type == "curator":
            from database import CuratorRepository
            all_staff = await CuratorRepository.get_all()
        elif staff_type == "teacher":
            from database import TeacherRepository
            all_staff = await TeacherRepository.get_all()
        else:
            raise ValueError(f"Неподдерживаемый тип сотрудников: {staff_type}")

    except Exception as e:
        print(f"Ошибка при получении {staff_type}: {e}")
        all_staff = []

    # Группируем персонал по user_id, чтобы избежать дублирования
    unique_staff = {}
    for staff_member in all_staff:
        user_id = staff_member.user_id
        if user_id not in unique_staff:
            unique_staff[user_id] = staff_member

    buttons = []
    for staff_member in unique_staff.values():
        # Показываем имя и предметы через группы
        staff_info = f"{staff_member.user.name}"

        # Получаем уникальные предметы через группы
        if hasattr(staff_member, 'groups') and staff_member.groups:
            subject_names = set()
            for group in staff_member.groups:
                if group.subject:
                    subject_names.add(group.subject.name)

            if subject_names:
                subjects_text = ", ".join(sorted(subject_names))
                staff_info += f" ({subjects_text})"

        buttons.append([
            InlineKeyboardButton(
                text=staff_info,
                callback_data=f"{callback_prefix}{staff_member.id}"
            )
        ])

    if not buttons:
        buttons.append([
            InlineKeyboardButton(
                text=f"❌ {staff_name_plural} не найдены",
                callback_data=f"no_{staff_type}s"
            )
        ])

    buttons.extend(get_main_menu_back_button())

    return InlineKeyboardMarkup(inline_keyboard=buttons)


async def get_curators_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора куратора (обратная совместимость)"""
    return await get_staff_kb("curator")

async def get_subjects_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора предмета"""
    # Получаем реальные предметы из базы данных
    try:
        subjects = await SubjectRepository.get_all()
    except Exception as e:
        print(f"Ошибка при получении предметов: {e}")
        subjects = []

    buttons = []
    for subject in subjects:
        buttons.append([
            InlineKeyboardButton(
                text=subject.name,
                callback_data=f"manager_subject_{subject.id}"
            )
        ])

    if not buttons:
        buttons.append([
            InlineKeyboardButton(
                text="❌ Предметы не найдены",
                callback_data="no_subjects"
            )
        ])

    buttons.extend(get_main_menu_back_button())

    return InlineKeyboardMarkup(inline_keyboard=buttons)


async def get_courses_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора курсов для менеджера"""
    from database import CourseRepository

    try:
        courses = await CourseRepository.get_all()
    except Exception as e:
        print(f"Ошибка при получении курсов: {e}")
        courses = []

    buttons = []
    for course in courses:
        buttons.append([
            InlineKeyboardButton(
                text=course.name,
                callback_data=f"manager_course_{course.id}"
            )
        ])

    if not buttons:
        buttons.append([
            InlineKeyboardButton(
                text="❌ Курсы не найдены",
                callback_data="no_courses"
            )
        ])

    buttons.extend(get_main_menu_back_button())

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def get_staff_type_selection_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора типа персонала (кураторы/преподаватели)"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="👨‍🎓 Кураторы", callback_data="staff_type_curator")],
        [InlineKeyboardButton(text="👨‍🏫 Преподаватели", callback_data="staff_type_teacher")],
        *get_main_menu_back_button()
    ])