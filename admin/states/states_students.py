from admin.handlers.students import AdminStudentsStates
from admin.handlers.main import admin_students_menu, AdminMainStates

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    # Состояния добавления ученика
    AdminStudentsStates.enter_student_name: AdminMainStates.students_menu,  # Возврат в меню учеников
    AdminStudentsStates.enter_student_telegram_id: AdminStudentsStates.enter_student_name,  # Возврат к вводу имени
    AdminStudentsStates.select_student_courses: AdminStudentsStates.enter_student_telegram_id,  # Возврат к вводу ID
    AdminStudentsStates.select_student_groups: AdminStudentsStates.select_student_courses,  # Возврат к выбору курсов
    AdminStudentsStates.select_student_tariff: AdminStudentsStates.select_student_groups,  # Возврат к выбору групп
    AdminStudentsStates.confirm_add_student: AdminStudentsStates.select_student_tariff,  # Возврат к выбору тарифа

    # Состояния удаления ученика (только через редактирование)
    AdminStudentsStates.confirm_delete_student: AdminStudentsStates.edit_student,  # Возврат к редактированию
    
    # Состояния просмотра и редактирования учеников
    AdminStudentsStates.students_list: AdminMainStates.students_menu,  # Возврат в меню учеников
    AdminStudentsStates.edit_student: AdminStudentsStates.students_list,  # Возврат к списку учеников
    AdminStudentsStates.enter_new_student_name: AdminStudentsStates.edit_student,  # Возврат к редактированию
    AdminStudentsStates.edit_student_courses: AdminStudentsStates.edit_student,  # Возврат к редактированию
    AdminStudentsStates.edit_student_groups: AdminStudentsStates.edit_student,  # Возврат к редактированию
    AdminStudentsStates.edit_student_tariff: AdminStudentsStates.edit_student,  # Возврат к редактированию
    AdminStudentsStates.confirm_edit_student: AdminStudentsStates.edit_student,  # Возврат к редактированию
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    AdminStudentsStates.enter_student_name: admin_students_menu,
    AdminStudentsStates.enter_student_telegram_id: admin_students_menu,
    AdminStudentsStates.select_student_courses: admin_students_menu,
    AdminStudentsStates.select_student_groups: admin_students_menu,
    AdminStudentsStates.select_student_tariff: admin_students_menu,
    AdminStudentsStates.confirm_add_student: admin_students_menu,
    AdminStudentsStates.confirm_delete_student: admin_students_menu,
    AdminStudentsStates.students_list: admin_students_menu,
    AdminStudentsStates.edit_student: admin_students_menu,
    AdminStudentsStates.enter_new_student_name: admin_students_menu,
    AdminStudentsStates.edit_student_courses: admin_students_menu,
    AdminStudentsStates.edit_student_groups: admin_students_menu,
    AdminStudentsStates.edit_student_tariff: admin_students_menu,
    AdminStudentsStates.confirm_edit_student: admin_students_menu,
}
