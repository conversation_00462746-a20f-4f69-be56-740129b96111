from admin.handlers.groups import AdminGroupsStates
from admin.handlers.main import admin_groups_menu, AdminMainStates

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    # Состояния добавления группы
    AdminGroupsStates.enter_group_name: AdminMainStates.groups_menu,  # Возврат в меню групп
    AdminGroupsStates.select_group_subject: AdminGroupsStates.enter_group_name,  # Возврат к вводу названия
    AdminGroupsStates.confirm_add_group: AdminGroupsStates.select_group_subject,  # Возврат к выбору предмета

    # Состояния удаления группы (только через редактирование)
    AdminGroupsStates.confirm_delete_group: AdminGroupsStates.edit_group,  # Возврат к редактированию
    
    # Состояния просмотра и редактирования групп
    AdminGroupsStates.groups_list: AdminMainStates.groups_menu,  # Возврат в меню групп
    AdminGroupsStates.edit_group: AdminGroupsStates.groups_list,  # Возврат к списку групп
    AdminGroupsStates.enter_new_group_name: AdminGroupsStates.edit_group,  # Возврат к редактированию
    AdminGroupsStates.select_new_group_subject: AdminGroupsStates.edit_group,  # Возврат к редактированию
    AdminGroupsStates.confirm_edit_group: AdminGroupsStates.edit_group,  # Возврат к редактированию
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    AdminGroupsStates.enter_group_name: admin_groups_menu,
    AdminGroupsStates.select_group_subject: admin_groups_menu,
    AdminGroupsStates.confirm_add_group: admin_groups_menu,
    AdminGroupsStates.confirm_delete_group: admin_groups_menu,
    AdminGroupsStates.groups_list: admin_groups_menu,
    AdminGroupsStates.edit_group: admin_groups_menu,
    AdminGroupsStates.enter_new_group_name: admin_groups_menu,
    AdminGroupsStates.select_new_group_subject: admin_groups_menu,
    AdminGroupsStates.confirm_edit_group: admin_groups_menu,
}
