from admin.handlers.teachers import AdminTeachersStates
from admin.handlers.main import admin_teachers_menu, AdminMainStates

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    # Состояния добавления преподавателя
    AdminTeachersStates.enter_teacher_name: AdminMainStates.teachers_menu,  # Возврат в меню преподавателей
    AdminTeachersStates.enter_teacher_telegram_id: AdminTeachersStates.enter_teacher_name,  # Возврат к вводу имени
    AdminTeachersStates.select_teacher_courses: AdminTeachersStates.enter_teacher_telegram_id,  # Возврат к вводу ID
    AdminTeachersStates.select_teacher_subjects: AdminTeachersStates.select_teacher_courses,  # Возврат к выбору курсов
    AdminTeachersStates.select_teacher_groups: AdminTeachersStates.select_teacher_subjects,  # Возврат к выбору предметов
    AdminTeachersStates.confirm_add_teacher: AdminTeachersStates.select_teacher_groups,  # Возврат к выбору групп

    # Состояния удаления преподавателя (только через редактирование)
    AdminTeachersStates.confirm_delete_teacher: AdminTeachersStates.edit_teacher,  # Возврат к редактированию
    
    # Состояния просмотра и редактирования преподавателей
    AdminTeachersStates.teachers_list: AdminMainStates.teachers_menu,  # Возврат в меню преподавателей
    AdminTeachersStates.edit_teacher: AdminTeachersStates.teachers_list,  # Возврат к списку преподавателей
    AdminTeachersStates.enter_new_teacher_name: AdminTeachersStates.edit_teacher,  # Возврат к редактированию
    AdminTeachersStates.edit_teacher_groups: AdminTeachersStates.edit_teacher,  # Возврат к редактированию
    AdminTeachersStates.confirm_edit_teacher: AdminTeachersStates.edit_teacher,  # Возврат к редактированию
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    AdminTeachersStates.enter_teacher_name: admin_teachers_menu,
    AdminTeachersStates.enter_teacher_telegram_id: admin_teachers_menu,
    AdminTeachersStates.select_teacher_courses: admin_teachers_menu,
    AdminTeachersStates.select_teacher_subjects: admin_teachers_menu,
    AdminTeachersStates.select_teacher_groups: admin_teachers_menu,
    AdminTeachersStates.confirm_add_teacher: admin_teachers_menu,
    AdminTeachersStates.confirm_delete_teacher: admin_teachers_menu,
    AdminTeachersStates.teachers_list: admin_teachers_menu,
    AdminTeachersStates.edit_teacher: admin_teachers_menu,
    AdminTeachersStates.enter_new_teacher_name: admin_teachers_menu,
    AdminTeachersStates.edit_teacher_groups: admin_teachers_menu,
    AdminTeachersStates.confirm_edit_teacher: admin_teachers_menu,
}
