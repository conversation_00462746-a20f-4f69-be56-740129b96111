from admin.handlers.managers import AdminManagersStates
from admin.handlers.main import admin_managers_menu, AdminMainStates

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    # Состояния добавления менеджера
    AdminManagersStates.enter_manager_name: AdminMainStates.managers_menu,  # Возврат в меню менеджеров
    AdminManagersStates.enter_manager_telegram_id: AdminManagersStates.enter_manager_name,  # Возврат к вводу имени
    AdminManagersStates.confirm_add_manager: AdminManagersStates.enter_manager_telegram_id,  # Возврат к вводу ID

    # Состояния удаления менеджера (только через редактирование)
    AdminManagersStates.confirm_delete_manager: AdminManagersStates.edit_manager,  # Возврат к редактированию
    
    # Состояния просмотра и редактирования менеджеров
    AdminManagersStates.managers_list: AdminMainStates.managers_menu,  # Возврат в меню менеджеров
    AdminManagersStates.edit_manager: AdminManagersStates.managers_list,  # Возврат к списку менеджеров
    AdminManagersStates.enter_new_manager_name: AdminManagersStates.edit_manager,  # Возврат к редактированию
    AdminManagersStates.confirm_edit_manager: AdminManagersStates.edit_manager,  # Возврат к редактированию
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    AdminManagersStates.enter_manager_name: admin_managers_menu,
    AdminManagersStates.enter_manager_telegram_id: admin_managers_menu,
    AdminManagersStates.confirm_add_manager: admin_managers_menu,
    AdminManagersStates.confirm_delete_manager: admin_managers_menu,
    AdminManagersStates.managers_list: admin_managers_menu,
    AdminManagersStates.edit_manager: admin_managers_menu,
    AdminManagersStates.enter_new_manager_name: admin_managers_menu,
    AdminManagersStates.confirm_edit_manager: admin_managers_menu,
}
