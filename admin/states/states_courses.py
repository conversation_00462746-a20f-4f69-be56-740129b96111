from admin.handlers.courses import AdminCoursesStates
from admin.handlers.main import admin_courses_menu, AdminMainStates

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    # Состояния добавления курса
    AdminCoursesStates.enter_course_name: AdminMainStates.courses_menu,  # Возврат в меню курсов
    AdminCoursesStates.select_course_subjects: AdminCoursesStates.enter_course_name,  # Возврат к вводу названия
    AdminCoursesStates.confirm_add_course: AdminCoursesStates.select_course_subjects,  # Возврат к выбору предметов

    # Состояния удаления курса (только через редактирование)
    AdminCoursesStates.confirm_delete_course: AdminCoursesStates.edit_course,  # Возврат к редактированию
    
    # Состояния просмотра и редактирования курсов
    AdminCoursesStates.courses_list: AdminMainStates.courses_menu,  # Возврат в меню курсов
    AdminCoursesStates.edit_course: AdminCoursesStates.courses_list,  # Возврат к списку курсов
    AdminCoursesStates.enter_new_course_name: AdminCoursesStates.edit_course,  # Возврат к редактированию
    AdminCoursesStates.edit_course_subjects: AdminCoursesStates.edit_course,  # Возврат к редактированию
    AdminCoursesStates.confirm_edit_course: AdminCoursesStates.enter_new_course_name,  # Возврат к вводу названия
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    AdminCoursesStates.enter_course_name: admin_courses_menu,
    AdminCoursesStates.select_course_subjects: admin_courses_menu,
    AdminCoursesStates.confirm_add_course: admin_courses_menu,
    AdminCoursesStates.confirm_delete_course: admin_courses_menu,
    AdminCoursesStates.courses_list: admin_courses_menu,
    AdminCoursesStates.edit_course: admin_courses_menu,
    AdminCoursesStates.enter_new_course_name: admin_courses_menu,
    AdminCoursesStates.edit_course_subjects: admin_courses_menu,
    AdminCoursesStates.confirm_edit_course: admin_courses_menu,
}
