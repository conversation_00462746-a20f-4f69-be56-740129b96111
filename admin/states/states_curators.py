from admin.handlers.curators import AdminCuratorsStates
from admin.handlers.main import admin_curators_menu, AdminMainStates

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    # Состояния добавления куратора
    AdminCuratorsStates.enter_curator_name: AdminMainStates.curators_menu,  # Возврат в меню кураторов
    AdminCuratorsStates.enter_curator_telegram_id: AdminCuratorsStates.enter_curator_name,  # Возврат к вводу имени
    AdminCuratorsStates.select_curator_courses: AdminCuratorsStates.enter_curator_telegram_id,  # Возврат к вводу ID
    AdminCuratorsStates.select_curator_subjects: AdminCuratorsStates.select_curator_courses,  # Возврат к выбору курсов
    AdminCuratorsStates.select_curator_groups: AdminCuratorsStates.select_curator_subjects,  # Возврат к выбору предметов
    AdminCuratorsStates.confirm_add_curator: AdminCuratorsStates.select_curator_groups,  # Возврат к выбору групп

    # Состояния удаления куратора (только через редактирование)
    AdminCuratorsStates.confirm_delete_curator: AdminCuratorsStates.edit_curator,  # Возврат к редактированию
    
    # Состояния просмотра и редактирования кураторов
    AdminCuratorsStates.curators_list: AdminMainStates.curators_menu,  # Возврат в меню кураторов
    AdminCuratorsStates.edit_curator: AdminCuratorsStates.curators_list,  # Возврат к списку кураторов
    AdminCuratorsStates.enter_new_curator_name: AdminCuratorsStates.edit_curator,  # Возврат к редактированию
    AdminCuratorsStates.edit_curator_groups: AdminCuratorsStates.edit_curator,  # Возврат к редактированию
    AdminCuratorsStates.confirm_edit_curator: AdminCuratorsStates.edit_curator,  # Возврат к редактированию
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    AdminCuratorsStates.enter_curator_name: admin_curators_menu,
    AdminCuratorsStates.enter_curator_telegram_id: admin_curators_menu,
    AdminCuratorsStates.select_curator_courses: admin_curators_menu,
    AdminCuratorsStates.select_curator_subjects: admin_curators_menu,
    AdminCuratorsStates.select_curator_groups: admin_curators_menu,
    AdminCuratorsStates.confirm_add_curator: admin_curators_menu,
    AdminCuratorsStates.confirm_delete_curator: admin_curators_menu,
    AdminCuratorsStates.curators_list: admin_curators_menu,
    AdminCuratorsStates.edit_curator: admin_curators_menu,
    AdminCuratorsStates.enter_new_curator_name: admin_curators_menu,
    AdminCuratorsStates.edit_curator_groups: admin_curators_menu,
    AdminCuratorsStates.confirm_edit_curator: admin_curators_menu,
}
