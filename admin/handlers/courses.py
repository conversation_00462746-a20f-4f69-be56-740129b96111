from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import StateFilter

from admin.utils.common import (
    add_course, remove_course, update_course,
    get_subjects_list_kb, get_courses_list_kb, get_confirmation_kb,
    get_course_by_id, get_course_by_name, get_courses_list
)
from common.keyboards import back_to_main_button, get_home_kb

router = Router()

class AdminCoursesStates(StatesGroup):
    # Состояния для добавления курса
    enter_course_name = State()
    select_course_subjects = State()
    confirm_add_course = State()

    # Состояния для удаления курса (используется только при редактировании)
    confirm_delete_course = State()

    # Состояния для просмотра и редактирования курсов
    courses_list = State()
    edit_course = State()
    enter_new_course_name = State()
    edit_course_subjects = State()
    confirm_edit_course = State()

# === ДОБАВЛЕНИЕ КУРСА ===

@router.callback_query(F.data == "add_course")
async def start_add_course(callback: CallbackQuery, state: FSMContext):
    """Начать добавление курса"""
    await callback.message.edit_text(
        text="Введите название курса:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminCoursesStates.enter_course_name)

@router.message(StateFilter(AdminCoursesStates.enter_course_name))
async def process_course_name(message: Message, state: FSMContext):
    """Обработать ввод названия курса"""
    course_name = message.text.strip()

    # Проверяем, существует ли уже курс с таким названием
    try:
        existing_course = await get_course_by_name(course_name)
        if existing_course:
            await message.answer(
                text=f"❌ Курс с названием '{course_name}' уже существует!\n\n"
                     f"Введите другое название курса:",
                reply_markup=get_home_kb()
            )
            # Остаемся в том же состоянии для повторного ввода
            return
    except Exception as e:
        await message.answer(
            text=f"❌ Ошибка при проверке существования курса!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(course_name=course_name, selected_subject_ids=[])
    await state.set_state(AdminCoursesStates.select_course_subjects)

    await message.answer(
        text=f"Курс: {course_name}\n\n"
             f"Выберите предметы для курса (можно выбрать несколько):\n"
             f"Выбрано: 0",
        reply_markup=await get_subjects_selection_kb([])
    )

async def get_subjects_selection_kb(selected_subject_ids: list):
    """Клавиатура для выбора предметов с возможностью множественного выбора"""
    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
    from database import SubjectRepository

    buttons = []

    # Получаем все предметы из базы данных
    all_subjects = await SubjectRepository.get_all()

    for subject in all_subjects:
        if subject.id in selected_subject_ids:
            # Предмет уже выбран
            buttons.append([
                InlineKeyboardButton(
                    text=f"✅ {subject.name}",
                    callback_data=f"unselect_subject_{subject.id}"
                )
            ])
        else:
            # Предмет не выбран
            buttons.append([
                InlineKeyboardButton(
                    text=f"⬜ {subject.name}",
                    callback_data=f"select_subject_{subject.id}"
                )
            ])

    # Кнопки управления
    if selected_subject_ids:
        buttons.append([
            InlineKeyboardButton(text="✅ Готово", callback_data="finish_subject_selection")
        ])

    buttons.extend([
        back_to_main_button()
    ])

    return InlineKeyboardMarkup(inline_keyboard=buttons)

@router.callback_query(AdminCoursesStates.select_course_subjects, F.data.startswith("select_subject_"))
async def select_subject_for_course(callback: CallbackQuery, state: FSMContext):
    """Выбрать предмет для курса"""
    subject_id = int(callback.data.replace("select_subject_", ""))
    data = await state.get_data()

    selected_subject_ids = data.get("selected_subject_ids", [])
    if subject_id not in selected_subject_ids:
        selected_subject_ids.append(subject_id)

    await state.update_data(selected_subject_ids=selected_subject_ids)

    course_name = data.get("course_name", "")
    await callback.message.edit_text(
        text=f"Курс: {course_name}\n\n"
             f"Выберите предметы для курса (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_subject_ids)}",
        reply_markup=await get_subjects_selection_kb(selected_subject_ids)
    )

@router.callback_query(AdminCoursesStates.select_course_subjects, F.data.startswith("unselect_subject_"))
async def unselect_subject_for_course(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор предмета для курса"""
    subject_id = int(callback.data.replace("unselect_subject_", ""))
    data = await state.get_data()

    selected_subject_ids = data.get("selected_subject_ids", [])
    if subject_id in selected_subject_ids:
        selected_subject_ids.remove(subject_id)

    await state.update_data(selected_subject_ids=selected_subject_ids)

    course_name = data.get("course_name", "")
    await callback.message.edit_text(
        text=f"Курс: {course_name}\n\n"
             f"Выберите предметы для курса (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_subject_ids)}",
        reply_markup=await get_subjects_selection_kb(selected_subject_ids)
    )

@router.callback_query(AdminCoursesStates.select_course_subjects, F.data == "finish_subject_selection")
async def finish_subject_selection(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор предметов"""
    from database import SubjectRepository

    data = await state.get_data()
    course_name = data.get("course_name", "")
    selected_subject_ids = data.get("selected_subject_ids", [])

    # Получаем названия предметов по ID
    subjects_names = []
    for subject_id in selected_subject_ids:
        subject = await SubjectRepository.get_by_id(subject_id)
        if subject:
            subjects_names.append(subject.name)

    subjects_text = "\n".join([f"• {name}" for name in subjects_names])

    await state.set_state(AdminCoursesStates.confirm_add_course)
    await callback.message.edit_text(
        text=f"📋 Подтверждение создания курса:\n\n"
             f"Название: {course_name}\n"
             f"Предметы ({len(selected_subject_ids)}):\n{subjects_text}",
        reply_markup=get_confirmation_kb("add", "course")
    )

@router.callback_query(StateFilter(AdminCoursesStates.confirm_add_course), F.data.startswith("confirm_add_course_"))
async def confirm_add_course(callback: CallbackQuery, state: FSMContext):
    """Подтвердить добавление курса"""
    data = await state.get_data()
    course_name = data.get("course_name", "")
    selected_subject_ids = data.get("selected_subject_ids", [])

    course_id = await add_course(course_name, selected_subject_ids)

    await callback.message.edit_text(
        text=f"✅ Курс '{course_name}' успешно создан!\n"
             f"ID курса: {course_id}",
        reply_markup=get_home_kb()
    )
    await state.clear()

# === ПРОСМОТР И РЕДАКТИРОВАНИЕ КУРСОВ ===

@router.callback_query(F.data == "list_courses")
async def show_courses_list(callback: CallbackQuery, state: FSMContext):
    """Показать список курсов для редактирования"""
    try:
        # Получаем список курсов
        courses = await get_courses_list()

        if not courses:
            await callback.message.edit_text(
                text="📋 Список курсов пуст!\n\n"
                     "Сначала добавьте курсы для управления ими.",
                reply_markup=get_home_kb()
            )
            return

        # Создаем клавиатуру со списком курсов для редактирования
        courses_kb = await get_courses_list_kb("edit_course")
        await callback.message.edit_text(
            text="📚 Список курсов\n\n"
                 "Выберите курс для редактирования:",
            reply_markup=courses_kb
        )
        await state.set_state(AdminCoursesStates.courses_list)

    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке списка курсов!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminCoursesStates.courses_list), F.data.startswith("edit_course_"))
async def start_edit_course(callback: CallbackQuery, state: FSMContext):
    """Начать редактирование курса"""
    try:
        course_id = int(callback.data.replace("edit_course_", ""))
        course = await get_course_by_id(course_id)

        if not course:
            await callback.message.edit_text(
                text="❌ Курс не найден!",
                reply_markup=get_home_kb()
            )
            return

        # Получаем названия предметов курса
        subject_names = [subject.name for subject in course.subjects]
        subjects_text = ", ".join(subject_names) if subject_names else "Нет предметов"

        await state.update_data(
            course_to_edit=course_id,
            current_course_name=course.name,
            current_subject_ids=[subject.id for subject in course.subjects]
        )
        await state.set_state(AdminCoursesStates.edit_course)

        # Создаем клавиатуру для редактирования
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        edit_kb = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="✏️ Изменить название", callback_data="change_course_name")],
            [InlineKeyboardButton(text="📖 Изменить предметы", callback_data="change_course_subjects")],
            [InlineKeyboardButton(text="🗑 Удалить курс", callback_data=f"delete_course_{course_id}")],
            [InlineKeyboardButton(text="⬅️ Назад к списку", callback_data="back_to_courses_list")],
            *get_home_kb().inline_keyboard
        ])

        await callback.message.edit_text(
            text=f"📚 Редактирование курса\n\n"
                 f"📝 Название: {course.name}\n"
                 f"📖 Предметы: {subjects_text}\n"
                 f"🆔 ID: {course.id}\n\n"
                 f"Выберите действие:",
            reply_markup=edit_kb
        )

    except ValueError as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обработке ID курса!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке данных курса!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminCoursesStates.edit_course), F.data == "change_course_name")
async def start_change_course_name(callback: CallbackQuery, state: FSMContext):
    """Начать изменение названия курса"""
    data = await state.get_data()
    current_name = data.get("current_course_name", "")

    await callback.message.edit_text(
        text=f"✏️ Изменение названия курса\n\n"
             f"Текущее название: {current_name}\n\n"
             f"Введите новое название курса:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminCoursesStates.enter_new_course_name)

@router.callback_query(StateFilter(AdminCoursesStates.edit_course), F.data == "change_course_subjects")
async def start_change_course_subjects(callback: CallbackQuery, state: FSMContext):
    """Начать изменение предметов курса"""
    data = await state.get_data()
    current_subject_ids = data.get("current_subject_ids", [])
    course_name = data.get("current_course_name", "")

    await callback.message.edit_text(
        text=f"📖 Изменение предметов курса '{course_name}'\n\n"
             f"Выберите предметы для курса (можно выбрать несколько):\n"
             f"Выбрано: {len(current_subject_ids)}",
        reply_markup=await get_subjects_selection_kb(current_subject_ids)
    )
    await state.update_data(selected_subject_ids=current_subject_ids)
    await state.set_state(AdminCoursesStates.edit_course_subjects)

@router.callback_query(AdminCoursesStates.edit_course_subjects, F.data.startswith("select_subject_"))
async def select_subject_for_course_edit(callback: CallbackQuery, state: FSMContext):
    """Выбрать предмет для курса при редактировании"""
    subject_id = int(callback.data.replace("select_subject_", ""))
    data = await state.get_data()
    course_name = data.get("current_course_name", "")

    selected_subject_ids = data.get("selected_subject_ids", [])
    if subject_id not in selected_subject_ids:
        selected_subject_ids.append(subject_id)

    await state.update_data(selected_subject_ids=selected_subject_ids)

    await callback.message.edit_text(
        text=f"📖 Изменение предметов курса '{course_name}'\n\n"
             f"Выберите предметы для курса (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_subject_ids)}",
        reply_markup=await get_subjects_selection_kb(selected_subject_ids)
    )

@router.callback_query(AdminCoursesStates.edit_course_subjects, F.data.startswith("unselect_subject_"))
async def unselect_subject_for_course_edit(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор предмета для курса при редактировании"""
    subject_id = int(callback.data.replace("unselect_subject_", ""))
    data = await state.get_data()
    course_name = data.get("current_course_name", "")

    selected_subject_ids = data.get("selected_subject_ids", [])
    if subject_id in selected_subject_ids:
        selected_subject_ids.remove(subject_id)

    await state.update_data(selected_subject_ids=selected_subject_ids)

    await callback.message.edit_text(
        text=f"📖 Изменение предметов курса '{course_name}'\n\n"
             f"Выберите предметы для курса (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_subject_ids)}",
        reply_markup=await get_subjects_selection_kb(selected_subject_ids)
    )

@router.callback_query(AdminCoursesStates.edit_course_subjects, F.data == "finish_subject_selection")
async def finish_subject_selection_for_edit(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор предметов при редактировании курса"""
    from database import SubjectRepository

    data = await state.get_data()
    course_name = data.get("current_course_name", "")
    course_id = data.get("course_to_edit")
    selected_subject_ids = data.get("selected_subject_ids", [])
    current_subject_ids = data.get("current_subject_ids", [])

    # Проверяем, изменился ли состав предметов
    if set(selected_subject_ids) == set(current_subject_ids):
        await callback.message.edit_text(
            text=f"⚠️ Состав предметов не изменился!\n\n"
                 f"Выберите другие предметы или вернитесь к редактированию курса.",
            reply_markup=get_home_kb()
        )
        return

    # Получаем названия предметов по ID
    subjects_names = []
    for subject_id in selected_subject_ids:
        subject = await SubjectRepository.get_by_id(subject_id)
        if subject:
            subjects_names.append(subject.name)

    # Получаем названия текущих предметов
    current_subjects_names = []
    for subject_id in current_subject_ids:
        subject = await SubjectRepository.get_by_id(subject_id)
        if subject:
            current_subjects_names.append(subject.name)

    await state.update_data(new_subject_ids=selected_subject_ids)
    await state.set_state(AdminCoursesStates.confirm_edit_course)

    await callback.message.edit_text(
        text=f"📋 Подтверждение изменения предметов курса:\n\n"
             f"Курс: {course_name}\n\n"
             f"Текущие предметы: {', '.join(current_subjects_names) if current_subjects_names else 'Нет предметов'}\n\n"
             f"Новые предметы: {', '.join(subjects_names) if subjects_names else 'Нет предметов'}",
        reply_markup=get_confirmation_kb("edit", "course", str(course_id))
    )

@router.message(StateFilter(AdminCoursesStates.enter_new_course_name))
async def process_new_course_name(message: Message, state: FSMContext):
    """Обработать ввод нового названия курса"""
    new_course_name = message.text.strip()
    data = await state.get_data()
    current_name = data.get("current_course_name", "")
    course_id = data.get("course_to_edit")

    # Проверяем, не совпадает ли новое название с текущим
    if new_course_name == current_name:
        await message.answer(
            text=f"⚠️ Новое название совпадает с текущим!\n\n"
                 f"Введите другое название курса:",
            reply_markup=get_home_kb()
        )
        return

    # Проверяем, существует ли уже курс с таким названием
    try:
        existing_course = await get_course_by_name(new_course_name)
        if existing_course:
            await message.answer(
                text=f"❌ Курс с названием '{new_course_name}' уже существует!\n\n"
                     f"Введите другое название курса:",
                reply_markup=get_home_kb()
            )
            return
    except Exception as e:
        await message.answer(
            text=f"❌ Ошибка при проверке существования курса!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(new_course_name=new_course_name)
    await state.set_state(AdminCoursesStates.confirm_edit_course)

    await message.answer(
        text=f"📋 Подтверждение изменения курса:\n\n"
             f"Текущее название: {current_name}\n"
             f"Новое название: {new_course_name}",
        reply_markup=get_confirmation_kb("edit", "course", str(course_id))
    )

@router.callback_query(StateFilter(AdminCoursesStates.confirm_edit_course), F.data.startswith("confirm_edit_course_"))
async def confirm_edit_course(callback: CallbackQuery, state: FSMContext):
    """Подтвердить редактирование курса"""
    data = await state.get_data()
    course_id = data.get("course_to_edit")
    new_name = data.get("new_course_name")
    new_subject_ids = data.get("new_subject_ids")
    current_name = data.get("current_course_name", "")

    try:
        # Определяем, что именно обновляем
        if new_name and new_subject_ids is not None:
            # Обновляем и название, и предметы
            success = await update_course(course_id, new_name, new_subject_ids)
            success_text = f"✅ Курс успешно обновлен!\n\n" \
                          f"Название изменено: {current_name} → {new_name}\n" \
                          f"Предметы также обновлены"
        elif new_name:
            # Обновляем только название
            success = await update_course(course_id, new_name)
            success_text = f"✅ Название курса успешно обновлено!\n\n" \
                          f"Старое название: {current_name}\n" \
                          f"Новое название: {new_name}"
        elif new_subject_ids is not None:
            # Обновляем только предметы
            success = await update_course(course_id, subject_ids=new_subject_ids)
            success_text = f"✅ Предметы курса '{current_name}' успешно обновлены!"
        else:
            # Ничего не обновляем
            await callback.message.edit_text(
                text="⚠️ Нет данных для обновления!",
                reply_markup=get_home_kb()
            )
            await state.clear()
            return

        if success:
            await callback.message.edit_text(
                text=success_text,
                reply_markup=get_home_kb()
            )
        else:
            await callback.message.edit_text(
                text=f"❌ Ошибка при обновлении курса!\n"
                     f"Возможно, курс с таким названием уже существует.",
                reply_markup=get_home_kb()
            )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обновлении курса!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

    await state.clear()

@router.callback_query(StateFilter(AdminCoursesStates.edit_course), F.data == "back_to_courses_list")
async def back_to_courses_list(callback: CallbackQuery, state: FSMContext):
    """Вернуться к списку курсов"""
    # Вызываем функцию показа списка курсов
    await show_courses_list(callback, state)

# === ОБРАБОТЧИКИ ОТМЕНЫ РЕДАКТИРОВАНИЯ ===

@router.callback_query(StateFilter(AdminCoursesStates.confirm_edit_course), F.data.startswith("cancel_edit_course"))
async def cancel_edit_course(callback: CallbackQuery, state: FSMContext):
    """Отменить редактирование курса"""
    await callback.message.edit_text(
        text="❌ Редактирование курса отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

# === УДАЛЕНИЕ КУРСА (только через редактирование) ===

@router.callback_query(StateFilter(AdminCoursesStates.edit_course), F.data.startswith("delete_course_"))
async def select_course_to_delete(callback: CallbackQuery, state: FSMContext):
    """Выбрать курс для удаления из режима редактирования"""
    from database import CourseRepository, SubjectRepository

    course_id = int(callback.data.replace("delete_course_", ""))
    course = await CourseRepository.get_by_id(course_id)

    if not course:
        await callback.message.edit_text(
            text="❌ Курс не найден!",
            reply_markup=get_home_kb()
        )
        return

    # Получаем предметы курса
    subjects = await SubjectRepository.get_by_course(course_id)
    subjects_text = "\n".join([f"• {subject.name}" for subject in subjects])

    await state.update_data(course_to_delete=course_id, course_name=course.name)
    await state.set_state(AdminCoursesStates.confirm_delete_course)

    await callback.message.edit_text(
        text=f"🗑 Подтверждение удаления курса:\n\n"
             f"Название: {course.name}\n"
             f"Предметы:\n{subjects_text}\n\n"
             f"⚠️ Это действие нельзя отменить!",
        reply_markup=get_confirmation_kb("delete", "course", str(course_id))
    )

@router.callback_query(StateFilter(AdminCoursesStates.confirm_delete_course), F.data.startswith("confirm_delete_course_"))
async def confirm_delete_course(callback: CallbackQuery, state: FSMContext):
    """Подтвердить удаление курса"""
    data = await state.get_data()
    course_id = data.get("course_to_delete")
    course_name = data.get("course_name", "Неизвестный курс")

    success = await remove_course(course_id)

    if success:
        await callback.message.edit_text(
            text=f"✅ Курс '{course_name}' успешно удален!",
            reply_markup=get_home_kb()
        )
    else:
        await callback.message.edit_text(
            text=f"❌ Курс '{course_name}' не найден!",
            reply_markup=get_home_kb()
        )

    await state.clear()

# === ОБРАБОТЧИКИ ОТМЕНЫ ===

@router.callback_query(StateFilter(AdminCoursesStates.confirm_add_course), F.data.startswith("cancel_add_course"))
async def cancel_add_course(callback: CallbackQuery, state: FSMContext):
    """Отменить добавление курса"""
    await callback.message.edit_text(
        text="❌ Добавление курса отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

@router.callback_query(StateFilter(AdminCoursesStates.confirm_delete_course), F.data.startswith("cancel_delete_course"))
async def cancel_delete_course(callback: CallbackQuery, state: FSMContext):
    """Отменить удаление курса"""
    await callback.message.edit_text(
        text="❌ Удаление курса отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

# === ОТМЕНА ДЕЙСТВИЙ ===

@router.callback_query(F.data.startswith("cancel_add_course") | F.data.startswith("cancel_delete_course"))
async def cancel_course_action(callback: CallbackQuery, state: FSMContext):
    """Отменить действие с курсом"""
    await callback.message.edit_text(
        text="❌ Действие отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()
