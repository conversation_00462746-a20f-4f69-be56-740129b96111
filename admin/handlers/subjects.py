import logging
from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import StateFilter

from admin.utils.common import add_subject, remove_subject, update_subject, get_confirmation_kb, get_subjects_list_kb, get_subjects_list, get_subject_by_id, get_subject_by_name
from common.keyboards import back_to_main_button, get_home_kb

async def log(name, role, state):
    logging.info(f"ВЫЗОВ: {name} | РОЛЬ: {role} | СОСТОЯНИЕ: {await state.get_state()}")

router = Router()

class AdminSubjectsStates(StatesGroup):
    # Состояния для добавления предмета
    enter_subject_name = State()
    confirm_add_subject = State()

    # Состояния для удаления предмета (используется только при редактировании)
    confirm_delete_subject = State()

    # Состояния для просмотра и редактирования предметов
    subjects_list = State()
    edit_subject = State()
    enter_new_subject_name = State()
    confirm_edit_subject = State()

# === ДОБАВЛЕНИЕ ПРЕДМЕТА ===

@router.callback_query(F.data == "add_subject")
async def start_add_subject(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Начать добавление предмета"""
    await log("start_add_subject", user_role, state)
    await callback.message.edit_text(
        text="Введите название предмета:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminSubjectsStates.enter_subject_name)
    await log("start_add_subject_AFTER", user_role, state)

@router.message(StateFilter(AdminSubjectsStates.enter_subject_name))
async def process_subject_name(message: Message, state: FSMContext, user_role: str = None):
    """Обработать ввод названия предмета"""
    await log("process_subject_name", user_role, state)
    subject_name = message.text.strip()

    # Проверяем, существует ли уже предмет с таким названием
    try:
        existing_subject = await get_subject_by_name(subject_name)
        if existing_subject:
            await message.answer(
                text=f"❌ Предмет с названием '{subject_name}' уже существует!\n\n"
                     f"Введите другое название предмета:",
                reply_markup=get_home_kb()
            )
            # Остаемся в том же состоянии для повторного ввода
            return
    except Exception as e:
        await message.answer(
            text=f"❌ Ошибка при проверке существования предмета!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(subject_name=subject_name)
    await state.set_state(AdminSubjectsStates.confirm_add_subject)

    await message.answer(
        text=f"📋 Подтверждение создания предмета:\n\n"
             f"Название: {subject_name}",
        reply_markup=get_confirmation_kb("add", "subject")
    )
    await log("process_subject_name_AFTER", user_role, state)

@router.callback_query(StateFilter(AdminSubjectsStates.confirm_add_subject), F.data.startswith("confirm_add_subject_"))
async def confirm_add_subject(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Подтвердить добавление предмета"""
    await log("confirm_add_subject", user_role, state)
    data = await state.get_data()
    subject_name = data.get("subject_name", "")

    try:
        success = await add_subject(subject_name)
        if success:
            await callback.message.edit_text(
                text=f"✅ Предмет '{subject_name}' успешно создан!",
                reply_markup=get_home_kb()
            )
        else:
            await callback.message.edit_text(
                text=f"❌ Ошибка при создании предмета '{subject_name}'!",
                reply_markup=get_home_kb()
            )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при создании предмета '{subject_name}'!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

    await state.clear()
    await log("confirm_add_subject_AFTER", user_role, state)

# === УДАЛЕНИЕ ПРЕДМЕТА (только через редактирование) ===

@router.callback_query(StateFilter(AdminSubjectsStates.edit_subject), F.data.startswith("delete_subject_"))
async def select_subject_to_delete(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Выбрать предмет для удаления из режима редактирования"""
    await log("select_subject_to_delete", user_role, state)

    try:
        subject_id = int(callback.data.replace("delete_subject_", ""))
        subject = await get_subject_by_id(subject_id)

        if not subject:
            await callback.message.edit_text(
                text="❌ Предмет не найден!",
                reply_markup=get_home_kb()
            )
            return

        await state.update_data(subject_to_delete=subject_id, subject_name=subject.name)
        await state.set_state(AdminSubjectsStates.confirm_delete_subject)

        await callback.message.edit_text(
            text=f"🗑 Подтверждение удаления предмета:\n\n"
                 f"Название: {subject.name}\n\n"
                 f"⚠️ Это действие нельзя отменить!",
            reply_markup=get_confirmation_kb("delete", "subject", str(subject_id))
        )

    except ValueError as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обработке ID предмета!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке данных предмета!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

    await log("select_subject_to_delete_AFTER", user_role, state)

@router.callback_query(StateFilter(AdminSubjectsStates.confirm_delete_subject), F.data.startswith("confirm_delete_subject_"))
async def confirm_delete_subject(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Подтвердить удаление предмета"""
    await log("confirm_delete_subject", user_role, state)
    data = await state.get_data()
    subject_id = data.get("subject_to_delete")
    subject_name = data.get("subject_name", "Неизвестный предмет")

    success = await remove_subject(subject_id)

    if success:
        await callback.message.edit_text(
            text=f"✅ Предмет '{subject_name}' успешно удален!",
            reply_markup=get_home_kb()
        )
    else:
        await callback.message.edit_text(
            text=f"❌ Ошибка при удалении предмета '{subject_name}'!",
            reply_markup=get_home_kb()
        )

    await state.clear()
    await log("confirm_delete_subject_AFTER", user_role, state)

# === ОБРАБОТЧИКИ ОТМЕНЫ ===

@router.callback_query(StateFilter(AdminSubjectsStates.confirm_add_subject), F.data.startswith("cancel_add_subject"))
async def cancel_add_subject(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Отменить добавление предмета"""
    await log("cancel_add_subject", user_role, state)
    await callback.message.edit_text(
        text="❌ Добавление предмета отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()
    await log("cancel_add_subject_AFTER", user_role, state)

@router.callback_query(StateFilter(AdminSubjectsStates.confirm_delete_subject), F.data.startswith("cancel_delete_subject"))
async def cancel_delete_subject(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Отменить удаление предмета"""
    await log("cancel_delete_subject", user_role, state)
    await callback.message.edit_text(
        text="❌ Удаление предмета отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()
    await log("cancel_delete_subject_AFTER", user_role, state)

# === ПРОСМОТР И РЕДАКТИРОВАНИЕ ПРЕДМЕТОВ ===

@router.callback_query(F.data == "list_subjects")
async def show_subjects_list(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Показать список предметов для редактирования"""
    await log("show_subjects_list", user_role, state)

    try:
        # Получаем список предметов
        subjects = await get_subjects_list()

        if not subjects:
            await callback.message.edit_text(
                text="📋 Список предметов пуст!\n\n"
                     "Сначала добавьте предметы для управления ими.",
                reply_markup=get_home_kb()
            )
            return

        # Создаем клавиатуру со списком предметов для редактирования
        subjects_kb = await get_subjects_list_kb("edit_subject")
        await callback.message.edit_text(
            text="📖 Список предметов\n\n"
                 "Выберите предмет для редактирования:",
            reply_markup=subjects_kb
        )
        await state.set_state(AdminSubjectsStates.subjects_list)

    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке списка предметов!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

    await log("show_subjects_list_AFTER", user_role, state)

@router.callback_query(StateFilter(AdminSubjectsStates.subjects_list), F.data.startswith("edit_subject_"))
async def start_edit_subject(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Начать редактирование предмета"""
    await log("start_edit_subject", user_role, state)

    try:
        subject_id = int(callback.data.replace("edit_subject_", ""))
        subject = await get_subject_by_id(subject_id)

        if not subject:
            await callback.message.edit_text(
                text="❌ Предмет не найден!",
                reply_markup=get_home_kb()
            )
            return

        await state.update_data(
            subject_to_edit=subject_id,
            current_subject_name=subject.name
        )
        await state.set_state(AdminSubjectsStates.edit_subject)

        # Создаем клавиатуру для редактирования
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        edit_kb = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="✏️ Изменить название", callback_data="change_subject_name")],
            [InlineKeyboardButton(text="🗑 Удалить предмет", callback_data=f"delete_subject_{subject_id}")],
            [InlineKeyboardButton(text="⬅️ Назад к списку", callback_data="back_to_subjects_list")],
            *get_home_kb().inline_keyboard
        ])

        await callback.message.edit_text(
            text=f"📖 Редактирование предмета\n\n"
                 f"📝 Название: {subject.name}\n"
                 f"🆔 ID: {subject.id}\n\n"
                 f"Выберите действие:",
            reply_markup=edit_kb
        )

    except ValueError as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обработке ID предмета!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке данных предмета!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

    await log("start_edit_subject_AFTER", user_role, state)

@router.callback_query(StateFilter(AdminSubjectsStates.edit_subject), F.data == "change_subject_name")
async def start_change_subject_name(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Начать изменение названия предмета"""
    await log("start_change_subject_name", user_role, state)

    data = await state.get_data()
    current_name = data.get("current_subject_name", "")

    await callback.message.edit_text(
        text=f"✏️ Изменение названия предмета\n\n"
             f"Текущее название: {current_name}\n\n"
             f"Введите новое название предмета:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminSubjectsStates.enter_new_subject_name)
    await log("start_change_subject_name_AFTER", user_role, state)

@router.message(StateFilter(AdminSubjectsStates.enter_new_subject_name))
async def process_new_subject_name(message: Message, state: FSMContext, user_role: str = None):
    """Обработать ввод нового названия предмета"""
    await log("process_new_subject_name", user_role, state)

    new_subject_name = message.text.strip()
    data = await state.get_data()
    current_name = data.get("current_subject_name", "")
    subject_id = data.get("subject_to_edit")

    # Проверяем, не совпадает ли новое название с текущим
    if new_subject_name == current_name:
        await message.answer(
            text=f"⚠️ Новое название совпадает с текущим!\n\n"
                 f"Введите другое название предмета:",
            reply_markup=get_home_kb()
        )
        return

    # Проверяем, существует ли уже предмет с таким названием
    try:
        existing_subject = await get_subject_by_name(new_subject_name)
        if existing_subject:
            await message.answer(
                text=f"❌ Предмет с названием '{new_subject_name}' уже существует!\n\n"
                     f"Введите другое название предмета:",
                reply_markup=get_home_kb()
            )
            return
    except Exception as e:
        await message.answer(
            text=f"❌ Ошибка при проверке существования предмета!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(new_subject_name=new_subject_name)
    await state.set_state(AdminSubjectsStates.confirm_edit_subject)

    await message.answer(
        text=f"📋 Подтверждение изменения предмета:\n\n"
             f"Текущее название: {current_name}\n"
             f"Новое название: {new_subject_name}",
        reply_markup=get_confirmation_kb("edit", "subject", str(subject_id))
    )
    await log("process_new_subject_name_AFTER", user_role, state)

@router.callback_query(StateFilter(AdminSubjectsStates.confirm_edit_subject), F.data.startswith("confirm_edit_subject_"))
async def confirm_edit_subject(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Подтвердить редактирование предмета"""
    await log("confirm_edit_subject", user_role, state)

    data = await state.get_data()
    subject_id = data.get("subject_to_edit")
    new_name = data.get("new_subject_name", "")
    current_name = data.get("current_subject_name", "")

    try:
        # Обновляем предмет в базе данных
        success = await update_subject(subject_id, new_name)

        if success:
            await callback.message.edit_text(
                text=f"✅ Предмет успешно обновлен!\n\n"
                     f"Старое название: {current_name}\n"
                     f"Новое название: {new_name}",
                reply_markup=get_home_kb()
            )
        else:
            await callback.message.edit_text(
                text=f"❌ Ошибка при обновлении предмета!\n"
                     f"Возможно, предмет с названием '{new_name}' уже существует.",
                reply_markup=get_home_kb()
            )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обновлении предмета!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

    await state.clear()
    await log("confirm_edit_subject_AFTER", user_role, state)


@router.callback_query(StateFilter(AdminSubjectsStates.edit_subject), F.data == "back_to_subjects_list")
async def back_to_subjects_list(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Вернуться к списку предметов"""
    await log("back_to_subjects_list", user_role, state)
    # Вызываем функцию показа списка предметов
    await show_subjects_list(callback, state, user_role)

# === ОБРАБОТЧИКИ ОТМЕНЫ РЕДАКТИРОВАНИЯ ===

@router.callback_query(StateFilter(AdminSubjectsStates.confirm_edit_subject), F.data.startswith("cancel_edit_subject"))
async def cancel_edit_subject(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """Отменить редактирование предмета"""
    await log("cancel_edit_subject", user_role, state)
    await callback.message.edit_text(
        text="❌ Редактирование предмета отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()
    await log("cancel_edit_subject_AFTER", user_role, state)
