from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import StateFilter

from admin.utils.common import (
    get_courses_list_kb, get_subjects_list_kb, get_groups_list_kb, get_curators_list_kb,
    get_confirmation_kb, add_curator, remove_curator, update_curator,
    get_course_by_id, get_subject_by_id, get_group_by_id,
    get_courses_selection_kb, get_subjects_selection_kb, get_groups_by_subjects_selection_kb,
    get_curator_by_id, get_curator_by_name, get_curators_list
)
from common.keyboards import get_home_kb

router = Router()

class AdminCuratorsStates(StatesGroup):
    # Состояния для добавления куратора
    enter_curator_name = State()
    enter_curator_telegram_id = State()
    select_curator_courses = State()  # Множественный выбор курсов
    select_curator_subjects = State()  # Множественный выбор предметов
    select_curator_groups = State()  # Множественный выбор групп
    confirm_add_curator = State()

    # Состояния для удаления куратора (используется только при редактировании)
    confirm_delete_curator = State()

    # Состояния для просмотра и редактирования кураторов
    curators_list = State()
    edit_curator = State()
    enter_new_curator_name = State()
    edit_curator_groups = State()
    confirm_edit_curator = State()

# === ДОБАВЛЕНИЕ КУРАТОРА ===

@router.callback_query(F.data == "add_curator")
async def start_add_curator(callback: CallbackQuery, state: FSMContext):
    """Начать добавление куратора"""
    await callback.message.edit_text(
        text="Введите имя и фамилию куратора:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminCuratorsStates.enter_curator_name)

@router.message(StateFilter(AdminCuratorsStates.enter_curator_name))
async def process_curator_name(message: Message, state: FSMContext):
    """Обработать ввод имени куратора"""
    curator_name = message.text.strip()

    # Проверяем, существует ли уже куратор с таким именем
    try:
        existing_curator = await get_curator_by_name(curator_name)
        if existing_curator:
            await message.answer(
                text=f"❌ Куратор с именем '{curator_name}' уже существует!\n\n"
                     f"Введите другое имя куратора:",
                reply_markup=get_home_kb()
            )
            # Остаемся в том же состоянии для повторного ввода
            return
    except Exception as e:
        await message.answer(
            text=f"❌ Ошибка при проверке существования куратора!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(curator_name=curator_name)
    await state.set_state(AdminCuratorsStates.enter_curator_telegram_id)

    await message.answer(
        text="Введите Telegram ID куратора:",
        reply_markup=get_home_kb()
    )

@router.message(StateFilter(AdminCuratorsStates.enter_curator_telegram_id))
async def process_curator_telegram_id(message: Message, state: FSMContext):
    """Обработать ввод Telegram ID куратора"""
    print(f"🔍 DEBUG: Обработчик curators.py вызван, telegram_id: {message.text}")
    try:
        telegram_id = int(message.text.strip())

        # Проверяем существующего пользователя с учетом возможности самоназначения админа
        from admin.utils.common import check_existing_user_for_role_assignment
        check_result = await check_existing_user_for_role_assignment(
            telegram_id, 'curator', message.from_user.id
        )

        print(f"🔍 DEBUG: check_result = {check_result}")

        if check_result['exists'] and not check_result['can_assign']:
            print(f"🔍 DEBUG: Блокируем добавление - пользователь существует и не может быть назначен")
            await message.answer(
                text=check_result['message'],
                reply_markup=get_home_kb()
            )
            return

        # Если пользователь существует и может быть назначен (админ добавляет себя)
        if check_result['exists'] and check_result['can_assign']:
            print(f"🔍 DEBUG: Разрешаем добавление - админ добавляет себя")
            await message.answer(
                text=check_result['message'] + "\n\nПродолжаем назначение роли куратора...",
                reply_markup=get_home_kb()
            )

        await state.update_data(curator_telegram_id=telegram_id, selected_course_ids=[])
        await state.set_state(AdminCuratorsStates.select_curator_courses)

        await message.answer(
            text="Выберите курсы для куратора (можно выбрать несколько):\nВыбрано: 0",
            reply_markup=await get_courses_selection_kb([])
        )
    except ValueError:
        await message.answer(
            text="❌ Telegram ID должен быть числом. Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )

# === МНОЖЕСТВЕННЫЙ ВЫБОР КУРСОВ ===

@router.callback_query(AdminCuratorsStates.select_curator_courses, F.data.startswith("select_course_"))
async def select_course_for_curator(callback: CallbackQuery, state: FSMContext):
    """Выбрать курс для куратора"""
    course_id = int(callback.data.replace("select_course_", ""))
    data = await state.get_data()

    selected_course_ids = data.get("selected_course_ids", [])
    if course_id not in selected_course_ids:
        selected_course_ids.append(course_id)

    await state.update_data(selected_course_ids=selected_course_ids)

    await callback.message.edit_text(
        text=f"Выберите курсы для куратора (можно выбрать несколько):\nВыбрано: {len(selected_course_ids)}",
        reply_markup=await get_courses_selection_kb(selected_course_ids)
    )

@router.callback_query(AdminCuratorsStates.select_curator_courses, F.data.startswith("unselect_course_"))
async def unselect_course_for_curator(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор курса для куратора"""
    course_id = int(callback.data.replace("unselect_course_", ""))
    data = await state.get_data()

    selected_course_ids = data.get("selected_course_ids", [])
    if course_id in selected_course_ids:
        selected_course_ids.remove(course_id)

    await state.update_data(selected_course_ids=selected_course_ids)

    await callback.message.edit_text(
        text=f"Выберите курсы для куратора (можно выбрать несколько):\nВыбрано: {len(selected_course_ids)}",
        reply_markup=await get_courses_selection_kb(selected_course_ids)
    )

@router.callback_query(AdminCuratorsStates.select_curator_courses, F.data == "finish_course_selection")
async def finish_course_selection_for_curator(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор курсов для куратора"""
    data = await state.get_data()
    selected_course_ids = data.get("selected_course_ids", [])

    if not selected_course_ids:
        await callback.answer("❌ Выберите хотя бы один курс!", show_alert=True)
        return

    # Получаем названия курсов для отображения
    course_names = []
    for course_id in selected_course_ids:
        course = await get_course_by_id(course_id)
        if course:
            course_names.append(course.name)

    await state.update_data(
        curator_course_ids=selected_course_ids,
        curator_course_names=course_names,
        selected_subject_ids=[]
    )
    await state.set_state(AdminCuratorsStates.select_curator_subjects)

    courses_text = ", ".join(course_names)
    await callback.message.edit_text(
        text=f"Курсы: {courses_text}\n\nВыберите предметы (можно выбрать несколько):\nВыбрано: 0",
        reply_markup=await get_subjects_selection_kb([], selected_course_ids)
    )

# === МНОЖЕСТВЕННЫЙ ВЫБОР ПРЕДМЕТОВ ===

@router.callback_query(AdminCuratorsStates.select_curator_subjects, F.data.startswith("select_subject_"))
async def select_subject_for_curator(callback: CallbackQuery, state: FSMContext):
    """Выбрать предмет для куратора"""
    subject_id = int(callback.data.replace("select_subject_", ""))
    data = await state.get_data()

    selected_subject_ids = data.get("selected_subject_ids", [])
    if subject_id not in selected_subject_ids:
        selected_subject_ids.append(subject_id)

    await state.update_data(selected_subject_ids=selected_subject_ids)

    course_names = data.get("curator_course_names", [])
    courses_text = ", ".join(course_names)
    selected_course_ids = data.get("selected_course_ids", [])

    await callback.message.edit_text(
        text=f"Курсы: {courses_text}\n\nВыберите предметы (можно выбрать несколько):\nВыбрано: {len(selected_subject_ids)}",
        reply_markup=await get_subjects_selection_kb(selected_subject_ids, selected_course_ids)
    )

@router.callback_query(AdminCuratorsStates.select_curator_subjects, F.data.startswith("unselect_subject_"))
async def unselect_subject_for_curator(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор предмета для куратора"""
    subject_id = int(callback.data.replace("unselect_subject_", ""))
    data = await state.get_data()

    selected_subject_ids = data.get("selected_subject_ids", [])
    if subject_id in selected_subject_ids:
        selected_subject_ids.remove(subject_id)

    await state.update_data(selected_subject_ids=selected_subject_ids)

    course_names = data.get("curator_course_names", [])
    courses_text = ", ".join(course_names)
    selected_course_ids = data.get("selected_course_ids", [])

    await callback.message.edit_text(
        text=f"Курсы: {courses_text}\n\nВыберите предметы (можно выбрать несколько):\nВыбрано: {len(selected_subject_ids)}",
        reply_markup=await get_subjects_selection_kb(selected_subject_ids, selected_course_ids)
    )

@router.callback_query(AdminCuratorsStates.select_curator_subjects, F.data == "finish_subject_selection")
async def finish_subject_selection_for_curator(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор предметов для куратора"""
    data = await state.get_data()
    selected_subject_ids = data.get("selected_subject_ids", [])

    if not selected_subject_ids:
        await callback.answer("❌ Выберите хотя бы один предмет!", show_alert=True)
        return

    # Получаем названия предметов для отображения
    subject_names = []
    for subject_id in selected_subject_ids:
        subject = await get_subject_by_id(subject_id)
        if subject:
            subject_names.append(subject.name)

    await state.update_data(
        curator_subject_ids=selected_subject_ids,
        curator_subject_names=subject_names,
        selected_group_ids=[]
    )
    await state.set_state(AdminCuratorsStates.select_curator_groups)

    course_names = data.get("curator_course_names", [])
    courses_text = ", ".join(course_names)
    subjects_text = ", ".join(subject_names)

    await callback.message.edit_text(
        text=f"Курсы: {courses_text}\nПредметы: {subjects_text}\n\n"
             f"Выберите группы для куратора (можно выбрать несколько):\nВыбрано: 0",
        reply_markup=await get_groups_by_subjects_selection_kb([], selected_subject_ids)
    )

# === МНОЖЕСТВЕННЫЙ ВЫБОР ГРУПП ===

@router.callback_query(AdminCuratorsStates.select_curator_groups, F.data.startswith("select_group_"))
async def select_group_for_curator(callback: CallbackQuery, state: FSMContext):
    """Выбрать группу для куратора"""
    group_id = int(callback.data.replace("select_group_", ""))
    data = await state.get_data()

    selected_group_ids = data.get("selected_group_ids", [])
    if group_id not in selected_group_ids:
        selected_group_ids.append(group_id)

    await state.update_data(selected_group_ids=selected_group_ids)

    course_names = data.get("curator_course_names", [])
    subject_names = data.get("curator_subject_names", [])
    selected_subject_ids = data.get("curator_subject_ids", [])

    courses_text = ", ".join(course_names)
    subjects_text = ", ".join(subject_names)

    await callback.message.edit_text(
        text=f"Курсы: {courses_text}\nПредметы: {subjects_text}\n\n"
             f"Выберите группы для куратора (можно выбрать несколько):\nВыбрано: {len(selected_group_ids)}",
        reply_markup=await get_groups_by_subjects_selection_kb(selected_group_ids, selected_subject_ids)
    )

@router.callback_query(AdminCuratorsStates.select_curator_groups, F.data.startswith("unselect_group_"))
async def unselect_group_for_curator(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор группы для куратора"""
    group_id = int(callback.data.replace("unselect_group_", ""))
    data = await state.get_data()

    selected_group_ids = data.get("selected_group_ids", [])
    if group_id in selected_group_ids:
        selected_group_ids.remove(group_id)

    await state.update_data(selected_group_ids=selected_group_ids)

    course_names = data.get("curator_course_names", [])
    subject_names = data.get("curator_subject_names", [])
    selected_subject_ids = data.get("curator_subject_ids", [])

    courses_text = ", ".join(course_names)
    subjects_text = ", ".join(subject_names)

    await callback.message.edit_text(
        text=f"Курсы: {courses_text}\nПредметы: {subjects_text}\n\n"
             f"Выберите группы для куратора (можно выбрать несколько):\nВыбрано: {len(selected_group_ids)}",
        reply_markup=await get_groups_by_subjects_selection_kb(selected_group_ids, selected_subject_ids)
    )

@router.callback_query(AdminCuratorsStates.select_curator_groups, F.data == "finish_group_selection")
async def finish_group_selection_for_curator(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор групп для куратора"""
    data = await state.get_data()
    selected_group_ids = data.get("selected_group_ids", [])

    if not selected_group_ids:
        await callback.answer("❌ Выберите хотя бы одну группу!", show_alert=True)
        return

    # Получаем названия групп по ID
    group_names = []
    for group_id in selected_group_ids:
        group = await get_group_by_id(group_id)
        if group:
            group_names.append(f"{group.name} ({group.subject.name})")

    groups_text = "\n".join([f"• {name}" for name in group_names])

    await state.update_data(curator_group_ids=selected_group_ids, curator_group_names=group_names)
    await state.set_state(AdminCuratorsStates.confirm_add_curator)

    curator_name = data.get("curator_name", "")
    telegram_id = data.get("curator_telegram_id", "")
    course_names = data.get("curator_course_names", [])
    subject_names = data.get("curator_subject_names", [])

    courses_text = ", ".join(course_names)
    subjects_text = ", ".join(subject_names)

    await callback.message.edit_text(
        text=f"📋 Подтверждение добавления куратора:\n\n"
             f"Имя: {curator_name}\n"
             f"Telegram ID: {telegram_id}\n"
             f"Курсы: {courses_text}\n"
             f"Предметы: {subjects_text}\n"
             f"Группы ({len(selected_group_ids)}):\n{groups_text}",
        reply_markup=get_confirmation_kb("add", "curator")
    )

@router.callback_query(StateFilter(AdminCuratorsStates.confirm_add_curator), F.data.startswith("confirm_add_curator_"))
async def confirm_add_curator(callback: CallbackQuery, state: FSMContext):
    """Подтвердить добавление куратора"""
    data = await state.get_data()

    curator_name = data.get("curator_name", "")
    telegram_id = data.get("curator_telegram_id", "")
    group_ids = data.get("curator_group_ids", [])

    # Добавляем куратора (теперь без course_id и subject_id)
    success = await add_curator(curator_name, telegram_id, group_ids)

    if success:
        group_names = data.get("curator_group_names", [])
        # Убираем предмет из названия группы для отображения
        clean_group_names = [name.split(" (")[0] for name in group_names]
        await callback.message.edit_text(
            text=f"✅ Куратор '{curator_name}' успешно добавлен в группы: {', '.join(clean_group_names)}!",
            reply_markup=get_home_kb()
        )
    else:
        await callback.message.edit_text(
            text=f"❌ Ошибка при добавлении куратора '{curator_name}'!\nВозможно, пользователь с таким Telegram ID уже существует.",
            reply_markup=get_home_kb()
        )

    await state.clear()

# === ПРОСМОТР И РЕДАКТИРОВАНИЕ КУРАТОРОВ ===

@router.callback_query(F.data == "list_curators")
async def show_curators_list(callback: CallbackQuery, state: FSMContext):
    """Показать список кураторов для редактирования"""
    try:
        # Получаем список кураторов
        curators = await get_curators_list()

        if not curators:
            await callback.message.edit_text(
                text="📋 Список кураторов пуст!\n\n"
                     "Сначала добавьте кураторов для управления ими.",
                reply_markup=get_home_kb()
            )
            return

        # Создаем клавиатуру со списком кураторов для редактирования
        curators_kb = await get_curators_list_kb("edit_curator")
        await callback.message.edit_text(
            text="👨‍🏫 Список кураторов\n\n"
                 "Выберите куратора для редактирования:",
            reply_markup=curators_kb
        )
        await state.set_state(AdminCuratorsStates.curators_list)

    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке списка кураторов!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminCuratorsStates.curators_list), F.data.startswith("edit_curator_"))
async def start_edit_curator(callback: CallbackQuery, state: FSMContext):
    """Начать редактирование куратора"""
    try:
        curator_id = int(callback.data.replace("edit_curator_", ""))
        curator = await get_curator_by_id(curator_id)

        if not curator:
            await callback.message.edit_text(
                text="❌ Куратор не найден!",
                reply_markup=get_home_kb()
            )
            return

        # Получаем названия групп куратора
        group_names = [f"{group.name} ({group.subject.name})" for group in curator.groups]
        groups_text = ", ".join(group_names) if group_names else "Нет групп"

        await state.update_data(
            curator_to_edit=curator_id,
            current_curator_name=curator.user.name,
            current_group_ids=[group.id for group in curator.groups]
        )
        await state.set_state(AdminCuratorsStates.edit_curator)

        # Создаем клавиатуру для редактирования
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
        edit_kb = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="✏️ Изменить имя", callback_data="change_curator_name")],
            [InlineKeyboardButton(text="👥 Изменить группы", callback_data="change_curator_groups")],
            [InlineKeyboardButton(text="🗑 Удалить куратора", callback_data=f"delete_curator_{curator_id}")],
            [InlineKeyboardButton(text="⬅️ Назад к списку", callback_data="back_to_curators_list")],
            *get_home_kb().inline_keyboard
        ])

        await callback.message.edit_text(
            text=f"👨‍🏫 Редактирование куратора\n\n"
                 f"📝 Имя: {curator.user.name}\n"
                 f"🆔 Telegram ID: {curator.user.telegram_id}\n"
                 f"👥 Группы: {groups_text}\n"
                 f"🆔 ID: {curator.id}\n\n"
                 f"Выберите действие:",
            reply_markup=edit_kb
        )

    except ValueError as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обработке ID куратора!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке данных куратора!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminCuratorsStates.edit_curator), F.data == "change_curator_name")
async def start_change_curator_name(callback: CallbackQuery, state: FSMContext):
    """Начать изменение имени куратора"""
    data = await state.get_data()
    current_name = data.get("current_curator_name", "")

    await callback.message.edit_text(
        text=f"✏️ Изменение имени куратора\n\n"
             f"Текущее имя: {current_name}\n\n"
             f"Введите новое имя куратора:",
        reply_markup=get_home_kb()
    )
    await state.set_state(AdminCuratorsStates.enter_new_curator_name)

@router.message(StateFilter(AdminCuratorsStates.enter_new_curator_name))
async def process_new_curator_name(message: Message, state: FSMContext):
    """Обработать ввод нового имени куратора"""
    new_curator_name = message.text.strip()
    data = await state.get_data()
    current_name = data.get("current_curator_name", "")
    curator_id = data.get("curator_to_edit")

    # Проверяем, не совпадает ли новое имя с текущим
    if new_curator_name == current_name:
        await message.answer(
            text=f"⚠️ Новое имя совпадает с текущим!\n\n"
                 f"Введите другое имя куратора:",
            reply_markup=get_home_kb()
        )
        return

    # Проверяем, существует ли уже куратор с таким именем
    try:
        existing_curator = await get_curator_by_name(new_curator_name)
        if existing_curator:
            await message.answer(
                text=f"❌ Куратор с именем '{new_curator_name}' уже существует!\n\n"
                     f"Введите другое имя куратора:",
                reply_markup=get_home_kb()
            )
            return
    except Exception as e:
        await message.answer(
            text=f"❌ Ошибка при проверке существования куратора!\n\n"
                 f"Причина: {str(e)}\n\n"
                 f"Попробуйте еще раз:",
            reply_markup=get_home_kb()
        )
        return

    await state.update_data(new_curator_name=new_curator_name)
    await state.set_state(AdminCuratorsStates.confirm_edit_curator)

    await message.answer(
        text=f"📋 Подтверждение изменения куратора:\n\n"
             f"Текущее имя: {current_name}\n"
             f"Новое имя: {new_curator_name}",
        reply_markup=get_confirmation_kb("edit", "curator", str(curator_id))
    )

@router.callback_query(StateFilter(AdminCuratorsStates.edit_curator), F.data == "change_curator_groups")
async def start_change_curator_groups(callback: CallbackQuery, state: FSMContext):
    """Начать изменение групп куратора"""
    data = await state.get_data()
    current_group_ids = data.get("current_group_ids", [])
    curator_name = data.get("current_curator_name", "")

    # Получаем все предметы для отображения всех групп
    from database import SubjectRepository
    all_subjects = await SubjectRepository.get_all()
    all_subject_ids = [subject.id for subject in all_subjects]

    await callback.message.edit_text(
        text=f"👥 Изменение групп куратора '{curator_name}'\n\n"
             f"Выберите группы для куратора (можно выбрать несколько):\n"
             f"Выбрано: {len(current_group_ids)}",
        reply_markup=await get_groups_by_subjects_selection_kb(current_group_ids, all_subject_ids)
    )
    await state.update_data(selected_group_ids=current_group_ids)
    await state.set_state(AdminCuratorsStates.edit_curator_groups)

@router.callback_query(AdminCuratorsStates.edit_curator_groups, F.data.startswith("select_group_"))
async def select_group_for_curator_edit(callback: CallbackQuery, state: FSMContext):
    """Выбрать группу для куратора при редактировании"""
    group_id = int(callback.data.replace("select_group_", ""))
    data = await state.get_data()
    curator_name = data.get("current_curator_name", "")

    selected_group_ids = data.get("selected_group_ids", [])
    if group_id not in selected_group_ids:
        selected_group_ids.append(group_id)

    await state.update_data(selected_group_ids=selected_group_ids)

    # Получаем все предметы для отображения всех групп
    from database import SubjectRepository
    all_subjects = await SubjectRepository.get_all()
    all_subject_ids = [subject.id for subject in all_subjects]

    await callback.message.edit_text(
        text=f"👥 Изменение групп куратора '{curator_name}'\n\n"
             f"Выберите группы для куратора (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_group_ids)}",
        reply_markup=await get_groups_by_subjects_selection_kb(selected_group_ids, all_subject_ids)
    )

@router.callback_query(AdminCuratorsStates.edit_curator_groups, F.data.startswith("unselect_group_"))
async def unselect_group_for_curator_edit(callback: CallbackQuery, state: FSMContext):
    """Отменить выбор группы для куратора при редактировании"""
    group_id = int(callback.data.replace("unselect_group_", ""))
    data = await state.get_data()
    curator_name = data.get("current_curator_name", "")

    selected_group_ids = data.get("selected_group_ids", [])
    if group_id in selected_group_ids:
        selected_group_ids.remove(group_id)

    await state.update_data(selected_group_ids=selected_group_ids)

    # Получаем все предметы для отображения всех групп
    from database import SubjectRepository
    all_subjects = await SubjectRepository.get_all()
    all_subject_ids = [subject.id for subject in all_subjects]

    await callback.message.edit_text(
        text=f"👥 Изменение групп куратора '{curator_name}'\n\n"
             f"Выберите группы для куратора (можно выбрать несколько):\n"
             f"Выбрано: {len(selected_group_ids)}",
        reply_markup=await get_groups_by_subjects_selection_kb(selected_group_ids, all_subject_ids)
    )

@router.callback_query(AdminCuratorsStates.edit_curator_groups, F.data == "finish_group_selection")
async def finish_group_selection_for_curator_edit(callback: CallbackQuery, state: FSMContext):
    """Завершить выбор групп при редактировании куратора"""
    from database import GroupRepository

    data = await state.get_data()
    curator_name = data.get("current_curator_name", "")
    curator_id = data.get("curator_to_edit")
    selected_group_ids = data.get("selected_group_ids", [])
    current_group_ids = data.get("current_group_ids", [])

    # Проверяем, изменился ли состав групп
    if set(selected_group_ids) == set(current_group_ids):
        await callback.message.edit_text(
            text=f"⚠️ Состав групп не изменился!\n\n"
                 f"Выберите другие группы или вернитесь к редактированию куратора.",
            reply_markup=get_home_kb()
        )
        return

    # Получаем названия групп по ID
    groups_names = []
    for group_id in selected_group_ids:
        group = await GroupRepository.get_by_id(group_id)
        if group:
            groups_names.append(f"{group.name} ({group.subject.name})")

    # Получаем названия текущих групп
    current_groups_names = []
    for group_id in current_group_ids:
        group = await GroupRepository.get_by_id(group_id)
        if group:
            current_groups_names.append(f"{group.name} ({group.subject.name})")

    await state.update_data(new_group_ids=selected_group_ids)
    await state.set_state(AdminCuratorsStates.confirm_edit_curator)

    await callback.message.edit_text(
        text=f"📋 Подтверждение изменения групп куратора:\n\n"
             f"Куратор: {curator_name}\n\n"
             f"Текущие группы: {', '.join(current_groups_names) if current_groups_names else 'Нет групп'}\n\n"
             f"Новые группы: {', '.join(groups_names) if groups_names else 'Нет групп'}",
        reply_markup=get_confirmation_kb("edit", "curator", str(curator_id))
    )

@router.callback_query(StateFilter(AdminCuratorsStates.confirm_edit_curator), F.data.startswith("confirm_edit_curator_"))
async def confirm_edit_curator(callback: CallbackQuery, state: FSMContext):
    """Подтвердить редактирование куратора"""
    data = await state.get_data()
    curator_id = data.get("curator_to_edit")
    new_name = data.get("new_curator_name")
    new_group_ids = data.get("new_group_ids")
    current_name = data.get("current_curator_name", "")

    try:
        # Определяем, что именно обновляем
        changes = []
        if new_name:
            changes.append(f"Имя: {current_name} → {new_name}")
        if new_group_ids is not None:
            changes.append("Группы обновлены")

        if not changes:
            await callback.message.edit_text(
                text="⚠️ Нет данных для обновления!",
                reply_markup=get_home_kb()
            )
            await state.clear()
            return

        # Обновляем куратора в базе данных
        success = await update_curator(
            curator_id,
            name=new_name,
            group_ids=new_group_ids
        )

        if success:
            await callback.message.edit_text(
                text=f"✅ Куратор успешно обновлен!\n\n" + "\n".join(changes),
                reply_markup=get_home_kb()
            )
        else:
            await callback.message.edit_text(
                text=f"❌ Ошибка при обновлении куратора!\n"
                     f"Возможно, куратор с таким именем уже существует.",
                reply_markup=get_home_kb()
            )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обновлении куратора!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

    await state.clear()

@router.callback_query(StateFilter(AdminCuratorsStates.edit_curator), F.data == "back_to_curators_list")
async def back_to_curators_list(callback: CallbackQuery, state: FSMContext):
    """Вернуться к списку кураторов"""
    # Вызываем функцию показа списка кураторов
    await show_curators_list(callback, state)

# === УДАЛЕНИЕ КУРАТОРА (только через редактирование) ===

@router.callback_query(StateFilter(AdminCuratorsStates.edit_curator), F.data.startswith("delete_curator_"))
async def select_curator_to_delete(callback: CallbackQuery, state: FSMContext):
    """Выбрать куратора для удаления из режима редактирования"""
    try:
        curator_id = int(callback.data.replace("delete_curator_", ""))
        curator = await get_curator_by_id(curator_id)

        if not curator:
            await callback.message.edit_text(
                text="❌ Куратор не найден!",
                reply_markup=get_home_kb()
            )
            return

        # Получаем названия групп куратора
        group_names = [f"{group.name} ({group.subject.name})" for group in curator.groups]
        groups_text = ", ".join(group_names) if group_names else "Нет групп"

        await state.update_data(
            curator_to_delete=curator_id,
            curator_name=curator.user.name
        )
        await state.set_state(AdminCuratorsStates.confirm_delete_curator)

        await callback.message.edit_text(
            text=f"🗑 Подтверждение удаления куратора:\n\n"
                 f"Имя: {curator.user.name}\n"
                 f"Telegram ID: {curator.user.telegram_id}\n"
                 f"Группы: {groups_text}\n\n"
                 f"⚠️ Это действие нельзя отменить!",
            reply_markup=get_confirmation_kb("delete", "curator", str(curator_id))
        )

    except ValueError as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при обработке ID куратора!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )
    except Exception as e:
        await callback.message.edit_text(
            text=f"❌ Ошибка при загрузке данных куратора!\n\n"
                 f"Причина: {str(e)}",
            reply_markup=get_home_kb()
        )

@router.callback_query(StateFilter(AdminCuratorsStates.confirm_delete_curator), F.data.startswith("confirm_delete_curator_"))
async def confirm_delete_curator(callback: CallbackQuery, state: FSMContext):
    """Подтвердить удаление куратора"""
    data = await state.get_data()
    curator_id = data.get("curator_to_delete")
    curator_name = data.get("curator_name", "")

    success = await remove_curator(curator_id)

    if success:
        await callback.message.edit_text(
            text=f"✅ Куратор '{curator_name}' успешно удален!",
            reply_markup=get_home_kb()
        )
    else:
        await callback.message.edit_text(
            text="❌ Куратор не найден!",
            reply_markup=get_home_kb()
        )

    await state.clear()

# === ОБРАБОТЧИКИ ОТМЕНЫ РЕДАКТИРОВАНИЯ ===

@router.callback_query(StateFilter(AdminCuratorsStates.confirm_edit_curator), F.data.startswith("cancel_edit_curator"))
async def cancel_edit_curator(callback: CallbackQuery, state: FSMContext):
    """Отменить редактирование куратора"""
    await callback.message.edit_text(
        text="❌ Редактирование куратора отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

# === ОТМЕНА ДЕЙСТВИЙ ===

@router.callback_query(StateFilter(AdminCuratorsStates.confirm_add_curator), F.data.startswith("cancel_add_curator"))
async def cancel_add_curator(callback: CallbackQuery, state: FSMContext):
    """Отменить добавление куратора"""
    await callback.message.edit_text(
        text="❌ Добавление куратора отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()

@router.callback_query(StateFilter(AdminCuratorsStates.confirm_delete_curator), F.data.startswith("cancel_delete_curator"))
async def cancel_delete_curator(callback: CallbackQuery, state: FSMContext):
    """Отменить удаление куратора"""
    await callback.message.edit_text(
        text="❌ Удаление куратора отменено",
        reply_markup=get_home_kb()
    )
    await state.clear()
