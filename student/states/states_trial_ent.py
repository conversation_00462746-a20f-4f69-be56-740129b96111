from student.handlers.trial_ent import (
    TrialEntStates, show_trial_ent_menu, choose_required_subjects,
    choose_profile_subjects, process_profile_subject,
    show_current_test_subjects, back_to_trial_ent_results,
    show_trial_ent_history, show_subject_analytics_menu
)

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    TrialEntStates.required_subjects: TrialEntStates.main,
    TrialEntStates.profile_subjects: TrialEntStates.required_subjects,
    TrialEntStates.second_profile_subject: TrialEntStates.profile_subjects,
    TrialEntStates.confirmation: TrialEntStates.main,
    TrialEntStates.test_in_progress: TrialEntStates.main,
    TrialEntStates.results: TrialEntStates.main,
    TrialEntStates.analytics_subjects: TrialEntStates.results,
    TrialEntStates.confirming_end: TrialEntStates.test_in_progress,
    TrialEntStates.history: TrialEntStates.main,
    TrialEntStates.history_detail: TrialEntStates.history,

    # Новые переходы для детальной аналитики
    TrialEntStates.subject_analytics_menu: TrialEntStates.analytics_subjects,
    TrialEntStates.subject_analytics_detailed: TrialEntStates.subject_analytics_menu,
    TrialEntStates.subject_analytics_summary: TrialEntStates.subject_analytics_menu,

    TrialEntStates.main: None  # None означает возврат в главное меню
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    TrialEntStates.main: show_trial_ent_menu,
    TrialEntStates.required_subjects: choose_required_subjects,
    TrialEntStates.profile_subjects: choose_profile_subjects,
    TrialEntStates.second_profile_subject: process_profile_subject,
    TrialEntStates.results: back_to_trial_ent_results,
    TrialEntStates.analytics_subjects: show_current_test_subjects,
    TrialEntStates.history: show_trial_ent_history,

    # Новые обработчики для детальной аналитики
    TrialEntStates.subject_analytics_menu: show_subject_analytics_menu,
    TrialEntStates.subject_analytics_detailed: show_subject_analytics_menu,  # Возврат к меню через check_if_id_in_callback_data
    TrialEntStates.subject_analytics_summary: show_subject_analytics_menu    # Возврат к меню через check_if_id_in_callback_data
}

