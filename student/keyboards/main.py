from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

def get_student_main_menu_kb() -> InlineKeyboardMarkup:
    """Клавиатура главного меню студента"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📝 Домашнее задание", callback_data="homework")],
        [InlineKeyboardButton(text="📊 Мой прогресс", callback_data="progress")],
        [InlineKeyboardButton(text="🎁 Магазин", callback_data="shop")],
        [InlineKeyboardButton(text="🧠 Тест-отчет", callback_data="student_tests")],
        [InlineKeyboardButton(text="🧪 Пробный ЕНТ", callback_data="trial_ent")],
        [InlineKeyboardButton(text="📞 Связь с куратором", callback_data="curator")],
        [InlineKeyboardButton(text="❓ Аккаунт", callback_data="account")]
    ])


async def get_student_menu_by_tariff(telegram_id: int) -> InlineKeyboardMarkup:
    """
    Получить клавиатуру меню студента - теперь показываем все кнопки независимо от тарифа

    Args:
        telegram_id: Telegram ID пользователя

    Returns:
        Полная клавиатура со всеми функциями
    """
    # Показываем все кнопки независимо от тарифа
    # Ограничения будут проверяться в обработчиках
    buttons = [
        [InlineKeyboardButton(text="📝 Домашнее задание", callback_data="homework")],
        [InlineKeyboardButton(text="📊 Мой прогресс", callback_data="progress")],
        [InlineKeyboardButton(text="🎁 Магазин", callback_data="shop")],
        [InlineKeyboardButton(text="🧠 Тест-отчет", callback_data="student_tests")],
        [InlineKeyboardButton(text="🧪 Пробный ЕНТ", callback_data="trial_ent")],
        [InlineKeyboardButton(text="📞 Связь с куратором", callback_data="curator")],
        [InlineKeyboardButton(text="❓ Аккаунт", callback_data="account")]
    ]

    return InlineKeyboardMarkup(inline_keyboard=buttons)