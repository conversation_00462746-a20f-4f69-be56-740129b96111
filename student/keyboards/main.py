from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

def get_student_main_menu_kb() -> InlineKeyboardMarkup:
    """Клавиатура главного меню студента"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📝 Домашнее задание", callback_data="homework")],
        [InlineKeyboardButton(text="📊 Мой прогресс", callback_data="progress")],
        [InlineKeyboardButton(text="🎁 Магазин", callback_data="shop")],
        [InlineKeyboardButton(text="🧠 Тест-отчет", callback_data="student_tests")],
        [InlineKeyboardButton(text="🧪 Пробный ЕНТ", callback_data="trial_ent")],
        [InlineKeyboardButton(text="📞 Связь с куратором", callback_data="curator")],
        [InlineKeyboardButton(text="❓ Аккаунт", callback_data="account")]
    ])


async def get_student_menu_by_tariff(telegram_id: int) -> InlineKeyboardMarkup:
    """
    Получить клавиатуру меню в зависимости от тарифа студента

    Args:
        telegram_id: Telegram ID пользователя

    Returns:
        Соответствующая клавиатура
    """
    from common.tariff_utils import get_student_tariff, TariffType

    tariff = await get_student_tariff(telegram_id)
    print(f"DEBUG: telegram_id={telegram_id}, tariff='{tariff}', STANDARD='{TariffType.STANDARD}', PREMIUM='{TariffType.PREMIUM}'")
    is_premium = tariff == TariffType.PREMIUM
    print(f"DEBUG: is_premium={is_premium}")

    # Базовые кнопки для всех
    buttons = [
        [InlineKeyboardButton(text="📝 Домашнее задание", callback_data="homework")],
        [InlineKeyboardButton(text="🎁 Магазин", callback_data="shop")],
        [InlineKeyboardButton(text="🧠 Тест-отчет", callback_data="student_tests")],
        [InlineKeyboardButton(text="❓ Аккаунт", callback_data="account")]
    ]

    # Добавляем премиум кнопки если нужно
    if is_premium:
        buttons.insert(1, [InlineKeyboardButton(text="📊 Мой прогресс", callback_data="progress")])
        buttons.insert(-1, [InlineKeyboardButton(text="🧪 Пробный ЕНТ", callback_data="trial_ent")])
        buttons.insert(-1, [InlineKeyboardButton(text="📞 Связь с куратором", callback_data="curator")])

    return InlineKeyboardMarkup(inline_keyboard=buttons)