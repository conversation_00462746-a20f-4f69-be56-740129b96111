from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.filters import CommandStart
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from ..keyboards.main import get_student_main_menu_kb, get_student_menu_by_tariff

router = Router()

class StudentMainStates(StatesGroup):
    main = State()

@router.message(CommandStart())
async def student_start(message: Message, state: FSMContext, user_role: str = None):
    """Начальное меню студента"""
    # Если роль не куратор, показываем меню студента
    if user_role == "student":
        await show_student_main_menu(message, state)

async def show_student_main_menu(message: Message, state: FSMContext = None, user_role: str = None):
    """Возврат в главное меню студента"""
    # Студенческое меню доступно всем (включая админов и новых пользователей)
    # Не делаем проверку роли, так как это меню по умолчанию

    # Получаем клавиатуру в зависимости от тарифа
    keyboard = await get_student_menu_by_tariff(message.from_user.id)

    await message.answer(
        "Привет 👋\n"
        "Здесь ты можешь проходить домашки, прокачивать темы, отслеживать свой прогресс и готовиться к ЕНТ.\n"
        "Ниже — все разделы, которые тебе доступны:",
        reply_markup=keyboard
    )

    if state:
        await state.set_state(StudentMainStates.main)