# Development Environment Configuration

# Telegram Bot
BOT_TOKEN=your_bot_token_here

# Database
POSTGRES_DB=telebot
POSTGRES_USER=telebot_user
POSTGRES_PASSWORD=dev_password_123

# Redis
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Webhook (отключен для разработки)
WEBHOOK_MODE=false
WEBHOOK_HOST=http://localhost:8000
WEBHOOK_PATH=/webhook
WEB_SERVER_HOST=0.0.0.0
WEB_SERVER_PORT=8000

# Development
ENVIRONMENT=development

# Настройки уведомлений о домашних заданиях
HOMEWORK_NOTIFICATION_ENABLED=true
HOMEWORK_DAYS_THRESHOLD=0.0007  # ~1 минута для быстрого тестирования (0.0007 дня = 1 минута)
HOMEWORK_CHECK_INTERVAL_MINUTES=1
HOMEWORK_DAILY_SUMMARY_HOUR=8
HOMEWORK_DAILY_SUMMARY_MINUTE=0

# Описание настроек уведомлений:
# HOMEWORK_NOTIFICATION_ENABLED - включить/выключить систему уведомлений
# HOMEWORK_DAYS_THRESHOLD - через сколько дней студент считается проблемным (может быть дробным)
#   Примеры: 5 = 5 дней, 0.0417 = 1 час, 0.0007 = 1 минута
# HOMEWORK_CHECK_INTERVAL_MINUTES - интервал проверки новых проблем (в минутах)
# HOMEWORK_DAILY_SUMMARY_HOUR - час отправки ежедневной сводки (по Алмате)
# HOMEWORK_DAILY_SUMMARY_MINUTE - минута отправки ежедневной сводки
