"""
Обработчики колбэков для уведомлений о домашних заданиях
"""
from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext

from database.repositories import StudentRepository
from curator.handlers.messages import MessageStates

router = Router()


@router.callback_query(F.data.startswith("send_message_to_student:"))
async def handle_send_message_to_student(callback: CallbackQuery, state: FSMContext):
    """Обработчик кнопки 'Отправить сообщение студенту' из уведомления"""
    try:
        # Извлекаем ID студента из callback_data
        student_id = int(callback.data.split(":")[1])
        print(f"DEBUG: Ищем студента с ID: {student_id}")

        # Получаем данные студента
        student = await StudentRepository.get_by_id(student_id)
        print(f"DEBUG: Найден студент: {student}")

        if not student:
            await callback.answer(f"❌ Студент с ID {student_id} не найден", show_alert=True)
            await state.clear()  # Очищаем состояние при ошибке
            return

        # Очищаем предыдущее состояние и сохраняем новые данные
        await state.clear()
        await state.update_data(selected_student_id=student_id)
        
        # Переходим в состояние ввода сообщения
        await state.set_state(MessageStates.enter_individual_message)
        
        # Отправляем сообщение с просьбой ввести текст
        await callback.message.answer(
            f"💬 <b>Отправка сообщения студенту</b>\n\n"
            f"👤 Получатель: <b>{student.user.name}</b>\n"
            f"📱 Telegram ID: {student.user.telegram_id}\n\n"
            f"✍️ Введите текст сообщения:",
            parse_mode='HTML'
        )
        
        await callback.answer()

    except Exception as e:
        await callback.answer("❌ Ошибка при обработке запроса", show_alert=True)
        await state.clear()  # Очищаем состояние при любой ошибке
        print(f"Ошибка в handle_send_message_to_student: {e}")


@router.message(MessageStates.enter_individual_message)
async def process_individual_message(message, state: FSMContext):
    """Обработка введенного сообщения для отправки студенту"""
    try:
        # Получаем данные из состояния
        data = await state.get_data()
        student_id = data.get('selected_student_id')

        print(f"DEBUG: Обрабатываем сообщение для студента ID: {student_id}")
        print(f"DEBUG: Данные в состоянии: {data}")
        
        if not student_id:
            await message.answer("❌ Ошибка: студент не выбран")
            await state.clear()
            return
        
        # Получаем данные студента
        student = await StudentRepository.get_by_id(student_id)
        if not student:
            await message.answer("❌ Студент не найден")
            await state.clear()
            return
        
        # Сохраняем текст сообщения в состояние
        await state.update_data(message_text=message.text)

        # Показываем превью с кнопками подтверждения
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="✅ Отправить", callback_data="confirm_send_message"),
                InlineKeyboardButton(text="❌ Отменить", callback_data="cancel_send_message")
            ]
        ])

        await message.answer(
            f"📝 <b>Проверьте сообщение</b>\n\n"
            f"👤 Получатель: <b>{student.user.name}</b>\n"
            f"📱 Telegram ID: {student.user.telegram_id}\n\n"
            f"💬 <b>Текст сообщения:</b>\n{message.text}",
            parse_mode='HTML',
            reply_markup=keyboard
        )

        # Переходим в состояние подтверждения
        await state.set_state(MessageStates.confirm_individual_message)
        
    except Exception as e:
        await message.answer("❌ Ошибка при отправке сообщения")
        await state.clear()
        print(f"Ошибка в process_individual_message: {e}")


@router.callback_query(MessageStates.confirm_individual_message, F.data == "confirm_send_message")
async def confirm_send_message(callback: CallbackQuery, state: FSMContext):
    """Подтверждение отправки сообщения студенту"""
    try:
        # Получаем данные из состояния
        data = await state.get_data()
        student_id = data.get('selected_student_id')
        message_text = data.get('message_text')

        if not student_id or not message_text:
            await callback.answer("❌ Ошибка: данные не найдены", show_alert=True)
            await state.clear()
            return

        # Получаем данные студента
        student = await StudentRepository.get_by_id(student_id)
        if not student:
            await callback.answer("❌ Студент не найден", show_alert=True)
            await state.clear()
            return

        # Формируем сообщение для студента
        student_message = f"📩 <b>Сообщение от куратора</b>\n\n{message_text}"

        # Отправляем сообщение студенту
        try:
            await callback.bot.send_message(
                chat_id=student.user.telegram_id,
                text=student_message,
                parse_mode='HTML'
            )

            # Подтверждение куратору
            await callback.message.edit_text(
                f"✅ <b>Сообщение отправлено!</b>\n\n"
                f"👤 Получатель: <b>{student.user.name}</b>\n"
                f"💬 Текст: {message_text}",
                parse_mode='HTML'
            )

        except Exception as e:
            await callback.message.edit_text(
                f"❌ <b>Ошибка отправки сообщения</b>\n\n"
                f"Возможные причины:\n"
                f"• Студент не писал боту /start\n"
                f"• Студент заблокировал бота\n"
                f"• Неверный Telegram ID\n\n"
                f"Детали ошибки: {str(e)}",
                parse_mode='HTML'
            )

        # Очищаем состояние
        await state.clear()
        await callback.answer()

    except Exception as e:
        await callback.answer("❌ Ошибка при отправке сообщения", show_alert=True)
        await state.clear()
        print(f"Ошибка в confirm_send_message: {e}")


@router.callback_query(MessageStates.confirm_individual_message, F.data == "cancel_send_message")
async def cancel_send_message(callback: CallbackQuery, state: FSMContext):
    """Отмена отправки сообщения"""
    try:
        await callback.message.edit_text(
            "❌ <b>Отправка сообщения отменена</b>",
            parse_mode='HTML'
        )

        await state.clear()
        await callback.answer()

    except Exception as e:
        await callback.answer("❌ Ошибка при отмене", show_alert=True)
        await state.clear()
        print(f"Ошибка в cancel_send_message: {e}")
