import logging
from aiogram import Router
from curator.states.states_homework import CuratorHomeworkStates
from common.homework.register_handlers import register_homework_handlers

# Настройка логгера
logger = logging.getLogger(__name__)

router = Router()

# Регистрируем обработчики для куратора
register_homework_handlers(router, CuratorHomeworkStates, "curator")

# Все обработчики теперь используются из общего модуля common/homework/

