from aiogram.fsm.state import StatesGroup, State

# Используем конкретные состояния для куратора
class CuratorHomeworkStates(StatesGroup):
    main = State()  # Главное состояние для входа в раздел домашних заданий
    homework_menu = State()
    student_stats_course = State()
    student_stats_group = State()
    student_stats_lesson = State()
    student_stats_list = State()
    group_stats_group = State()
    group_stats_result = State()
    # Состояния для отправки сообщений
    enter_homework_message = State()
    confirm_homework_message = State()

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    CuratorHomeworkStates.main: None,  # None означает возврат в главное меню куратора
    CuratorHomeworkStates.homework_menu: CuratorHomeworkStates.main,
    CuratorHomeworkStates.student_stats_course: CuratorHomeworkStates.homework_menu,
    CuratorHomeworkStates.student_stats_group: CuratorHomeworkStates.student_stats_course,
    CuratorHomeworkStates.student_stats_lesson: CuratorHomeworkStates.student_stats_group,
    CuratorHomeworkStates.student_stats_list: CuratorHomeworkStates.student_stats_lesson,
    CuratorHomeworkStates.group_stats_group: CuratorHomeworkStates.homework_menu,
    CuratorHomeworkStates.group_stats_result: CuratorHomeworkStates.group_stats_group,
}

# Словарь обработчиков для каждого состояния (будет заполнен после регистрации)
STATE_HANDLERS = {}
