# Пример конфигурации nginx для телеграм бота (если понадобится webhook)

server {
    listen 80;
    server_name your-domain.com;

    # Webhook для телеграм бота
    location /webhook {
        proxy_pass http://bot:8000;  # Имя сервиса бота в Docker
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Админ панель (если добавите веб-интерфейс)
    location /admin {
        proxy_pass http://bot:8001;  # Дополнительный порт бота для админки
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API эндпоинты (если добавите)
    location /api {
        proxy_pass http://bot:8002;  # Дополнительный порт бота для API
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Статические файлы (если понадобятся)
    location /static {
        alias /app/static;  # Путь внутри Docker контейнера
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}

# HTTPS версия (рекомендуется для webhook)
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/fullchain.pem;
    ssl_certificate_key /etc/ssl/privkey.pem;

    location /webhook {
        proxy_pass http://bot:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
