from common.student_tests.states import StudentTestsStates
from typing import Dict, Any, Callable
from common.student_tests.handlers import (
    handle_main,
    handle_test_in_progress,
    handle_course_entry_subjects,
    handle_course_entry_subject_selected,
    handle_course_entry_confirmation,
    handle_month_entry_subjects,
    handle_month_entry_subject_selected,
    handle_month_entry_month_selected,
    handle_month_entry_confirmation,
    handle_month_control_subjects,
    handle_month_control_subject_selected,
    handle_month_control_month_selected,
    handle_month_control_confirmation
)

# Обработчики для возврата к результатам тестов
async def handle_course_entry_result(callback, state=None, user_role: str = None):
    """Обработчик возврата к результату входного теста курса"""
    data = await state.get_data()
    test_result_id = data.get('course_entry_test_result_id')
    if test_result_id:
        from .course_entry_handlers import show_course_entry_test_results_by_id
        await show_course_entry_test_results_by_id(callback, state, test_result_id)
    else:
        # Если ID не найден, возвращаемся к выбору предмета
        await handle_course_entry_subjects(callback, state, user_role)

async def handle_month_entry_result(callback, state=None, user_role: str = None):
    """Обработчик возврата к результату входного теста месяца"""
    data = await state.get_data()
    test_result_id = data.get('month_entry_test_result_id')
    if test_result_id:
        from .base_handlers import show_month_entry_test_statistics_by_id
        await show_month_entry_test_statistics_by_id(callback, state, test_result_id)
    else:
        # Если ID не найден, возвращаемся к выбору месяца
        await handle_month_entry_month_selected(callback, state, user_role)

async def handle_month_control_result(callback, state=None, user_role: str = None):
    """Обработчик возврата к результату контрольного теста месяца"""
    data = await state.get_data()
    test_result_id = data.get('month_control_test_result_id')
    if test_result_id:
        from .base_handlers import show_month_control_test_statistics_by_id
        await show_month_control_test_statistics_by_id(callback, state, test_result_id)
    else:
        # Если ID не найден, возвращаемся к выбору месяца
        await handle_month_control_month_selected(callback, state, user_role)


# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    # Входной тест курса
    StudentTestsStates.course_entry_subjects: StudentTestsStates.main,
    StudentTestsStates.course_entry_subject_selected: StudentTestsStates.course_entry_subjects,
    StudentTestsStates.course_entry_confirmation: StudentTestsStates.course_entry_subject_selected,

    # Входной тест месяца
    StudentTestsStates.month_entry_subjects: StudentTestsStates.main,
    StudentTestsStates.month_entry_subject_selected: StudentTestsStates.month_entry_subjects,
    StudentTestsStates.month_entry_month_selected: StudentTestsStates.month_entry_subject_selected,
    StudentTestsStates.month_entry_confirmation: StudentTestsStates.month_entry_month_selected,

    # Контрольный тест месяца
    StudentTestsStates.month_control_subjects: StudentTestsStates.main,
    StudentTestsStates.month_control_subject_selected: StudentTestsStates.month_control_subjects,
    StudentTestsStates.month_control_month_selected: StudentTestsStates.month_control_subject_selected,
    StudentTestsStates.month_control_confirmation: StudentTestsStates.month_control_month_selected,

    # Общие состояния
    StudentTestsStates.test_in_progress: StudentTestsStates.main,  # Из теста возвращаемся в главное меню

    # Отдельные состояния результатов с логичными переходами
    StudentTestsStates.course_entry_result: StudentTestsStates.course_entry_subjects,  # К выбору предмета
    StudentTestsStates.month_entry_result: StudentTestsStates.month_entry_month_selected,  # К выбору месяца
    StudentTestsStates.month_control_result: StudentTestsStates.month_control_month_selected,  # К выбору месяца

    # Детальная статистика - возврат к результатам тестов
    StudentTestsStates.course_entry_detailed_stats: StudentTestsStates.course_entry_result,
    StudentTestsStates.course_entry_summary_stats: StudentTestsStates.course_entry_result,
    StudentTestsStates.month_entry_detailed_stats: StudentTestsStates.month_entry_result,
    StudentTestsStates.month_entry_summary_stats: StudentTestsStates.month_entry_result,
    StudentTestsStates.month_control_detailed_stats: StudentTestsStates.month_control_result,
    StudentTestsStates.month_control_summary_stats: StudentTestsStates.month_control_result,

    StudentTestsStates.main: None  # None означает возврат в главное меню студента
}

# Словарь обработчиков состояний
STATE_HANDLERS = {
    # Добавляем обработчик для главного меню тестов
    StudentTestsStates.main: handle_main,
    StudentTestsStates.test_in_progress: handle_test_in_progress,

    # Обработчики для отдельных состояний результатов
    StudentTestsStates.course_entry_result: handle_course_entry_result,
    StudentTestsStates.month_entry_result: handle_month_entry_result,
    StudentTestsStates.month_control_result: handle_month_control_result,

    # Обработчики детальной статистики - возврат к результатам
    StudentTestsStates.course_entry_detailed_stats: handle_course_entry_result,
    StudentTestsStates.course_entry_summary_stats: handle_course_entry_result,
    StudentTestsStates.month_entry_detailed_stats: handle_month_entry_result,
    StudentTestsStates.month_entry_summary_stats: handle_month_entry_result,
    StudentTestsStates.month_control_detailed_stats: handle_month_control_result,
    StudentTestsStates.month_control_summary_stats: handle_month_control_result,

    # Входной тест курса
    StudentTestsStates.course_entry_subjects: handle_course_entry_subjects,
    StudentTestsStates.course_entry_subject_selected: handle_course_entry_subject_selected,
    StudentTestsStates.course_entry_confirmation: handle_course_entry_confirmation,

    # Входной тест месяца
    StudentTestsStates.month_entry_subjects: handle_month_entry_subjects,
    StudentTestsStates.month_entry_subject_selected: handle_month_entry_subject_selected,
    StudentTestsStates.month_entry_month_selected: handle_month_entry_month_selected,
    StudentTestsStates.month_entry_confirmation: handle_month_entry_confirmation,

    # Контрольный тест месяца
    StudentTestsStates.month_control_subjects: handle_month_control_subjects,
    StudentTestsStates.month_control_subject_selected: handle_month_control_subject_selected,
    StudentTestsStates.month_control_month_selected: handle_month_control_month_selected,
    StudentTestsStates.month_control_confirmation: handle_month_control_confirmation
}