"""
Утилиты для проверки доступа к премиум функциям
"""
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.tariff_utils import get_student_tariff, TariffType
from common.keyboards import get_main_menu_back_button


async def check_premium_access(telegram_id: int) -> bool:
    """
    Проверить, имеет ли пользователь доступ к премиум функциям
    
    Args:
        telegram_id: Telegram ID пользователя
        
    Returns:
        True если есть доступ к премиум функциям, False иначе
    """
    tariff = await get_student_tariff(telegram_id)
    return tariff == TariffType.PREMIUM


def get_premium_required_kb() -> InlineKeyboardMarkup:
    """
    Клавиатура для сообщений о необходимости премиум тарифа
    """
    return InlineKeyboardMarkup(inline_keyboard=[
        *get_main_menu_back_button()
    ])


async def show_premium_required_message(callback, feature_name: str, description: str):
    """
    Показать сообщение о необходимости премиум тарифа
    
    Args:
        callback: CallbackQuery объект
        feature_name: Название функции
        description: Описание функции
    """
    message_text = f"{description}\n\n🔒 Этот раздел доступен в тарифе ⭐ Премиум"
    
    await callback.message.edit_text(
        text=message_text,
        reply_markup=get_premium_required_kb()
    )


# Описания премиум функций
PREMIUM_FEATURES = {
    "progress": {
        "name": "📊 Мой прогресс",
        "description": "📊 Мой прогресс\n\n"
                      "Отслеживай свой прогресс по каждому предмету:\n"
                      "• 📈 Детальная статистика по темам\n"
                      "• 🎯 Процент правильных ответов\n"
                      "• 📊 Графики улучшения знаний\n"
                      "• 🏆 Достижения и рекорды\n"
                      "• 📝 История выполненных заданий\n\n"
                      "Узнай свои слабые места и работай над ними целенаправленно!"
    },
    "trial_ent": {
        "name": "🧪 Пробный ЕНТ",
        "description": "🧪 Пробный ЕНТ\n\n"
                      "Готовься к настоящему ЕНТ с реалистичными условиями:\n"
                      "• ⏱️ Ограничение по времени как на реальном экзамене\n"
                      "• 📋 Полноценные тесты по всем предметам\n"
                      "• 📊 Подробный анализ результатов\n"
                      "• 🎯 Рекомендации по улучшению\n"
                      "• 📈 Отслеживание прогресса подготовки\n\n"
                      "Почувствуй себя уверенно на настоящем экзамене!"
    },
    "curator": {
        "name": "📞 Связь с куратором",
        "description": "📞 Связь с куратором\n\n"
                      "Получи персональную поддержку от опытного куратора:\n"
                      "• 💬 Прямая связь с куратором\n"
                      "• 📝 Персональные рекомендации по учебе\n"
                      "• 🎯 Помощь в планировании подготовки\n"
                      "• 📊 Разбор сложных тем\n"
                      "• 🚀 Мотивация и поддержка\n\n"
                      "Твой личный наставник на пути к успеху!"
    },
    "month_entry_test": {
        "name": "📊 Входной тест месяца",
        "description": "📊 Входной тест месяца\n\n"
                      "Берутся 30 случайных вопросов из домашних заданий выбранных предметов. Данный тест можно пройти только один раз!\n\n"
                      "Возможности входного теста:\n"
                      "• 🎯 Оценка текущего уровня знаний\n"
                      "• 📊 Определение слабых мест в начале месяца\n"
                      "• 📈 Планирование учебного процесса\n"
                      "• 🏆 Отслеживание прогресса от месяца к месяцу\n"
                      "• 📝 Персональные рекомендации по изучению тем\n\n"
                      "Узнай свой стартовый уровень и планируй обучение эффективно!"
    },
    "month_control_test": {
        "name": "📈 Контрольный тест месяца",
        "description": "📈 Контрольный тест месяца\n\n"
                      "Берутся 30 случайных вопросов из домашних заданий выбранных предметов. Данный тест можно пройти только один раз!\n\n"
                      "Возможности контрольного теста:\n"
                      "• ✅ Проверка усвоения материала за месяц\n"
                      "• 📊 Сравнение с входным тестом месяца\n"
                      "• 🎯 Оценка эффективности обучения\n"
                      "• 📈 Измерение прогресса в изучении предметов\n"
                      "• 🏆 Подтверждение достигнутого уровня знаний\n\n"
                      "Проверь, насколько хорошо ты усвоил материал за месяц!"
    },
    "detailed_analytics": {
        "name": "📊 Детальная аналитика",
        "description": "📊 Детальная аналитика\n\n"
                      "Глубокий анализ твоих знаний по каждой микротеме:\n"
                      "• 🔍 Подробная статистика по каждому вопросу\n"
                      "• 📈 Динамика улучшения знаний по темам\n"
                      "• 🎯 Точное определение проблемных областей\n"
                      "• 📊 Сравнение результатов разных тестов\n"
                      "• 💡 Персональные рекомендации по изучению\n\n"
                      "Узнай точно, над чем нужно работать для максимального результата!"
    },
    "advanced_statistics": {
        "name": "📈 Расширенная статистика",
        "description": "📈 Расширенная статистика\n\n"
                      "Профессиональный анализ твоей подготовки:\n"
                      "• 📊 Графики прогресса по времени\n"
                      "• 🏆 Рейтинг среди других учеников\n"
                      "• ⏱️ Анализ времени выполнения заданий\n"
                      "• 🎯 Прогнозирование результатов ЕНТ\n"
                      "• 📝 Детальные отчеты для родителей\n\n"
                      "Получи максимум информации для эффективной подготовки!"
    },
    "premium_homework": {
        "name": "📝 Премиум домашние задания",
        "description": "📝 Премиум домашние задания\n\n"
                      "Расширенные возможности для выполнения домашек:\n"
                      "• 🔄 Неограниченные попытки прохождения\n"
                      "• 📊 Детальный разбор каждого ответа\n"
                      "• 💡 Подсказки и объяснения к вопросам\n"
                      "• 🎯 Дополнительные задания по слабым темам\n"
                      "• ⏱️ Режим подготовки к экзамену с таймером\n\n"
                      "Изучай материал максимально эффективно!"
    }
}
