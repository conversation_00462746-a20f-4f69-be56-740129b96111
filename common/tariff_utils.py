"""
Утилиты для работы с тарифами студентов
"""
from typing import Optional
from database import StudentRepository, UserRepository


class TariffType:
    """Константы тарифов"""
    STANDARD = "📦 Стандарт"
    PREMIUM = "⭐ Премиум"


async def get_student_tariff(telegram_id: int) -> Optional[str]:
    """
    Получить тариф студента по telegram_id
    
    Args:
        telegram_id: Telegram ID пользователя
        
    Returns:
        Тариф студента или None если студент не найден
    """
    try:
        student = await StudentRepository.get_by_telegram_id(telegram_id)
        if student and student.tariff:
            return student.tariff
        return None
    except Exception:
        return None


async def is_premium_student(telegram_id: int) -> bool:
    """
    Проверить, является ли студент премиум пользователем
    
    Args:
        telegram_id: Telegram ID пользователя
        
    Returns:
        True если студент имеет премиум тариф, False иначе
    """
    tariff = await get_student_tariff(telegram_id)
    return tariff == TariffType.PREMIUM


async def is_standard_student(telegram_id: int) -> bool:
    """
    Проверить, является ли студент стандарт пользователем
    
    Args:
        telegram_id: Telegram ID пользователя
        
    Returns:
        True если студент имеет стандарт тариф, False иначе
    """
    tariff = await get_student_tariff(telegram_id)
    return tariff == TariffType.STANDARD
