"""
Управление состоянием активных вопросов quiz системы
Глобальные переменные для отслеживания активных и завершенных вопросов
"""

from typing import Dict, Set

# Глобальный словарь для отслеживания активных вопросов
# Структура: {question_uuid: {"chat_id": int, "state": FSMContext, "bot": Bot, "answered": bool}}
active_questions: Dict[str, Dict] = {}

# Множество для отслеживания завершенных вопросов (для избежания дублирования)
completed_questions: Set[str] = set()
