"""
Утилиты для quiz системы
Функции очистки данных и мониторинга состояния
"""

import logging

from .state_manager import active_questions, completed_questions


async def cleanup_orphaned_quiz_states():
    """Очистка зависших состояний quiz после перезагрузки системы"""
    try:
        # Очищаем все активные вопросы (они потеряли актуальность после перезагрузки)
        active_questions.clear()
        completed_questions.clear()

    except Exception as e:
        logging.error(f"❌ QUIZ: Ошибка при очистке зависших состояний: {e}")


async def cleanup_test_data(user_id: int):
    """Очистка данных завершенного теста"""
    try:
        # Очищаем активные вопросы для этого пользователя
        questions_to_remove = []
        for question_uuid, question_info in active_questions.items():
            if question_info.get("chat_id") == user_id:
                questions_to_remove.append(question_uuid)

        for question_uuid in questions_to_remove:
            del active_questions[question_uuid]

        # Очищаем старые завершенные вопросы (оставляем только последние 100)
        if len(completed_questions) > 100:
            completed_list = list(completed_questions)
            completed_questions.clear()
            completed_questions.update(completed_list[-50:])

    except Exception as e:
        logging.error(f"❌ QUIZ: Ошибка при очистке данных теста: {e}")


def get_active_questions_count() -> int:
    """Получить количество активных вопросов (для мониторинга)"""
    return len(active_questions)


def get_completed_questions_count() -> int:
    """Получить количество завершенных вопросов (для мониторинга)"""
    return len(completed_questions)
