events {
    worker_connections 1024;
}

http {
    # Базовая защита (aiogram сам управляет rate limiting)
    # limit_req_zone $binary_remote_addr zone=webhook:10m rate=10r/s;

    server {
        listen 80;
        server_name _;

        # Перенаправление на HTTPS
        return 301 https://$host$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name ${DOMAIN};  # Домен из переменной окружения

        # SSL сертификаты (автоматически монтируются из nginx/ssl/)
        ssl_certificate /etc/ssl/fullchain.pem;
        ssl_certificate_key /etc/ssl/privkey.pem;

        # SSL настройки для безопасности
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Webhook эндпоинт для Telegram
        location /webhook {
            # Rate limiting управляется aiogram'ом
            # limit_req zone=webhook burst=20 nodelay;

            # Проксирование к боту
            proxy_pass http://bot:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Таймауты
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;

            # Только POST запросы от Telegram
            if ($request_method != POST) {
                return 405;
            }

            # Убираем проверку User-Agent для Telegram webhook'ов
            # if ($http_user_agent = "") {
            #     return 403;
            # }
        }

        location /stats {
            proxy_pass http://bot:8000/stats;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # Healthcheck эндпоинт
        location /health {
            proxy_pass http://bot:8000/health;
            proxy_set_header Host $host;
            access_log off;
        }

        # Блокировка всех остальных запросов
        location / {
            return 404;
        }

        # Логирование (выводим в stdout/stderr для Docker)
        access_log /var/log/nginx/access.log;
        error_log /var/log/nginx/error.log;
    }
}
